#!/bin/bash

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Função para exibir menu avançado
show_advanced_menu() {
    echo -e "\n${CYAN}=== CONTROLE AVANÇADO DE SIMULAÇÃO ===${NC}"
    echo -e "${GREEN}1)${NC} Configurar peso específico"
    echo -e "${GREEN}2)${NC} Configurar padrão de oscilação"
    echo -e "${GREEN}3)${NC} Configurar tempo entre fases"
    echo -e "${GREEN}4)${NC} Executar fase única"
    echo -e "${GREEN}5)${NC} Modo de teste rápido"
    echo -e "${GREEN}6)${NC} Modo de teste lento"
    echo -e "${GREEN}7)${NC} Modo de peso, e pronto"
    echo -e "${GREEN}8)${NC} Salvar configuração personalizada"
    echo -e "${GREEN}9)${NC} Carregar configuração salva"
    echo -e "${GREEN}10)${NC} Voltar ao menu principal"
    echo -e "${CYAN}========================================${NC}"
}

# Função para configurar peso específico
configure_specific_weight() {
    echo -e "\n${YELLOW}=== CONFIGURAÇÃO DE PESO ESPECÍFICO ===${NC}"
    
    read -p "Digite o peso desejado (ex: 12345): " weight
    if [ -z "$weight" ]; then
        echo -e "${RED}Peso não pode ser vazio!${NC}"
        return
    fi
    
    # Formata o peso para o padrão da balança
    formatted_weight=$(printf "%05d" $weight)
    
    # Atualiza o arquivo de configuração
    if [ -f "/tmp/simulation_config.json" ]; then
        jq --arg weight "$formatted_weight   000   ${formatted_weight}000" '.custom_weight = $weight' /tmp/simulation_config.json > /tmp/simulation_config.json.tmp
        mv /tmp/simulation_config.json.tmp /tmp/simulation_config.json
        echo -e "${GREEN}Peso configurado: $formatted_weight${NC}"
    else
        echo -e "${RED}Arquivo de configuração não encontrado!${NC}"
    fi
}

# Função para configurar padrão de oscilação
configure_oscillation_pattern() {
    echo -e "\n${YELLOW}=== CONFIGURAÇÃO DE PADRÃO DE OSCILAÇÃO ===${NC}"
    
    echo -e "${BLUE}Padrões disponíveis:${NC}"
    echo "1) IIIIIIIIIIIIIIIIIIII (padrão padrão)"
    echo "2) 99999999999999999999 (nove)"
    echo "3) 00000000000000000000 (zero)"
    echo "4) Personalizado"
    
    read -p "Escolha o padrão (1-4): " pattern_choice
    
    case $pattern_choice in
        1)
            pattern="IIIIIIIIIIIIIIIIIIII"
            ;;
        2)
            pattern="99999999999999999999"
            ;;
        3)
            pattern="00000000000000000000"
            ;;
        4)
            read -p "Digite o padrão personalizado: " pattern
            ;;
        *)
            echo -e "${RED}Opção inválida!${NC}"
            return
            ;;
    esac
    
    # Atualiza o arquivo de configuração
    if [ -f "/tmp/simulation_config.json" ]; then
        jq --arg pattern "$pattern" '.oscillation_pattern = $pattern' /tmp/simulation_config.json > /tmp/simulation_config.json.tmp
        mv /tmp/simulation_config.json.tmp /tmp/simulation_config.json
        echo -e "${GREEN}Padrão de oscilação configurado: $pattern${NC}"
    else
        echo -e "${RED}Arquivo de configuração não encontrado!${NC}"
    fi
}

# Função para configurar tempo entre fases
configure_phase_timing() {
    echo -e "\n${YELLOW}=== CONFIGURAÇÃO DE TEMPO ENTRE FASES ===${NC}"
    
    read -p "Tempo entre oscilações (segundos): " osc_delay
    read -p "Tempo entre estabilidade (segundos): " stab_delay
    read -p "Tempo entre zero (segundos): " zero_delay
    read -p "Tempo entre ciclos (segundos): " cycle_delay
    
    # Atualiza o arquivo de configuração
    if [ -f "/tmp/simulation_config.json" ]; then
        jq --argjson osc "$osc_delay" --argjson stab "$stab_delay" --argjson zero "$zero_delay" --argjson cycle "$cycle_delay" '.oscillation_delay = $osc | .stability_delay = $stab | .zero_delay = $zero | .cycle_delay = $cycle' /tmp/simulation_config.json > /tmp/simulation_config.json.tmp
        mv /tmp/simulation_config.json.tmp /tmp/simulation_config.json
        echo -e "${GREEN}Timing configurado!${NC}"
    else
        echo -e "${RED}Arquivo de configuração não encontrado!${NC}"
    fi
}

# Função para executar fase única
execute_single_phase() {
    echo -e "\n${YELLOW}=== EXECUÇÃO DE FASE ÚNICA ===${NC}"
    
    echo -e "${BLUE}Fases disponíveis:${NC}"
    echo "1) Oscilação"
    echo "2) Estabilidade"
    echo "3) Zero"
    
    read -p "Escolha a fase (1-3): " phase_choice
    
    case $phase_choice in
        1)
            phase="oscillation"
            ;;
        2)
            phase="stability"
            ;;
        3)
            phase="zero"
            ;;
        *)
            echo -e "${RED}Opção inválida!${NC}"
            return
            ;;
    esac
    
    # Cria arquivo de comando para o Python
    echo "{\"command\": \"single_phase\", \"phase\": \"$phase\"}" > /tmp/simulation_command.json
    
    echo -e "${GREEN}Comando enviado para executar fase: $phase${NC}"
}

# Função para modo de teste rápido
quick_test_mode() {
    echo -e "\n${YELLOW}=== MODO DE TESTE RÁPIDO ===${NC}"
    
    # Configuração para teste rápido
    cat > /tmp/simulation_config.json << EOF
{
    "oscillation_count": 3,
    "stability_count": 2,
    "zero_count": 1,
    "delay": 0.1,
    "baudrate": 9600,
    "port": "auto"
}
EOF
    
    echo -e "${GREEN}Modo de teste rápido configurado!${NC}"
    echo -e "${BLUE}Configuração:${NC}"
    echo "  Oscilações: 3"
    echo "  Estabilidade: 2"
    echo "  Zero: 1"
    echo "  Delay: 0.1 segundos"
}

# Função para modo de teste lento
slow_test_mode() {
    echo -e "\n${YELLOW}=== MODO DE TESTE LENTO ===${NC}"
    
    # Configuração para teste lento
    cat > /tmp/simulation_config.json << EOF
{
    "oscillation_count": 20,
    "stability_count": 15,
    "zero_count": 5,
    "delay": 1.0,
    "baudrate": 9600,
    "port": "auto"
}
EOF
    
    echo -e "${GREEN}Modo de teste lento configurado!${NC}"
    echo -e "${BLUE}Configuração:${NC}"
    echo "  Oscilações: 20"
    echo "  Estabilidade: 15"
    echo "  Zero: 5"
    echo "  Delay: 1.0 segundos"
}

# Modo enviar apenas o peso
only_weight_mode() {
    echo -e "\n${YELLOW}=== MODO DE PESO, E PRONTO ===${NC}"
    
    # Configuração para teste lento
    cat > /tmp/simulation_config.json << EOF
{
    "oscillation_count": 20,
    "stability_count": 15,
    "zero_count": 5,
    "delay": 1.0,
    "baudrate": 9600,
    "port": "auto",
    "use_only_weight": true
}
EOF
    
    echo -e "${GREEN}Modo de peso e pronto configurado!${NC}"
    echo -e "${BLUE}Configuração:${NC}"
    echo "  Envio de peso sozinho: true"
    echo "  Oscilações: 20"
    echo "  Estabilidade: 15"
    echo "  Zero: 5"
    echo "  Delay: 1.0 segundos"
}

# Função para salvar configuração personalizada
save_custom_config() {
    echo -e "\n${YELLOW}=== SALVAR CONFIGURAÇÃO PERSONALIZADA ===${NC}"
    
    read -p "Nome da configuração: " config_name
    if [ -z "$config_name" ]; then
        echo -e "${RED}Nome não pode ser vazio!${NC}"
        return
    fi
    
    if [ -f "/tmp/simulation_config.json" ]; then
        cp /tmp/simulation_config.json "configs/${config_name}.json"
        echo -e "${GREEN}Configuração salva como: configs/${config_name}.json${NC}"
    else
        echo -e "${RED}Arquivo de configuração não encontrado!${NC}"
    fi
}

# Função para carregar configuração salva
load_saved_config() {
    echo -e "\n${YELLOW}=== CARREGAR CONFIGURAÇÃO SALVA ===${NC}"
    
    # Lista configurações disponíveis
    if [ -d "configs" ]; then
        echo -e "${BLUE}Configurações disponíveis:${NC}"
        ls -1 configs/*.json 2>/dev/null | sed 's|configs/||' | sed 's|.json||' | nl
    else
        echo -e "${YELLOW}Nenhuma configuração salva encontrada.${NC}"
        return
    fi
    
    read -p "Digite o número da configuração: " config_num
    
    # Carrega a configuração selecionada
    config_file=$(ls -1 configs/*.json 2>/dev/null | sed -n "${config_num}p")
    if [ -n "$config_file" ]; then
        cp "$config_file" /tmp/simulation_config.json
        echo -e "${GREEN}Configuração carregada: $config_file${NC}"
    else
        echo -e "${RED}Configuração não encontrada!${NC}"
    fi
}

# Função principal
main() {
    # Cria diretório de configurações se não existir
    mkdir -p configs
    
    while true; do
        show_advanced_menu
        read -p "Escolha uma opção: " choice
        
        case $choice in
            1)
                configure_specific_weight
                ;;
            2)
                configure_oscillation_pattern
                ;;
            3)
                configure_phase_timing
                ;;
            4)
                execute_single_phase
                ;;
            5)
                quick_test_mode
                ;;
            6)
                slow_test_mode
                ;;
            7)
                only_weight_mode
                ;;
            8)
                save_custom_config
                ;;
            9)
                load_saved_config
                ;;
            10)
                echo -e "${GREEN}Retornando ao menu principal...${NC}"
                break
                ;;
            *)
                echo -e "${RED}Opção inválida!${NC}"
                ;;
        esac
        
        echo -e "\n${CYAN}Pressione Enter para continuar...${NC}"
        read
    done
}

# Executa o script se chamado diretamente
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main
fi 
