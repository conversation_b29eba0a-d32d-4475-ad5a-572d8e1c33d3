#!/bin/bash

# Cores para output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${YELLOW}=== TESTE RÁPIDO DA SIMULAÇÃO ===${NC}"

# Verifica se o script principal existe
if [ ! -f "scripts/setup_testing_environment.sh" ]; then
    echo -e "${RED}Erro: Script principal não encontrado!${NC}"
    exit 1
fi

# Verifica se o script Python existe
if [ ! -f "tests/write_ports_interactive.py" ]; then
    echo -e "${RED}Erro: Script Python não encontrado!${NC}"
    exit 1
fi

# Verifica dependências
echo -e "${YELLOW}Verificando dependências...${NC}"

if ! command -v socat &> /dev/null; then
    echo -e "${RED}Erro: socat não está instalado${NC}"
    echo "Instale com: sudo apt install socat"
    exit 1
fi

if ! command -v jq &> /dev/null; then
    echo -e "${RED}Erro: jq não está instalado${NC}"
    echo "Instale com: sudo apt install jq"
    exit 1
fi

if ! command -v python &> /dev/null; then
    echo -e "${RED}Erro: python não está instalado${NC}"
    exit 1
fi

echo -e "${GREEN}✓ Todas as dependências estão instaladas${NC}"

echo -e "\n${YELLOW}Para usar o sistema de simulação:${NC}"
echo -e "${GREEN}1. Execute: ./scripts/setup_testing_environment.sh${NC}"
echo -e "${GREEN}2. Use o menu para controlar a simulação${NC}"
echo -e "${GREEN}3. Para parar: escolha opção 3${NC}"
echo -e "${GREEN}4. Para sair: escolha opção 0${NC}"

echo -e "\n${YELLOW}Dicas:${NC}"
echo "- A simulação roda em background"
echo "- Use Ctrl+C para interromper o script bash"
echo "- Os arquivos temporários são limpos automaticamente"
echo "- Para monitorar a impressora, use outro terminal" 