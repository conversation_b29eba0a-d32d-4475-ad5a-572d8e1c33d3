#!/bin/bash

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configurações padrão
DEFAULT_OSCILLATION_COUNT=10
DEFAULT_STABILITY_COUNT=7
DEFAULT_ZERO_COUNT=3
DEFAULT_DELAY=0.3
DEFAULT_BAUDRATE=9600
DEFAULT_WEIGHT_ONLY_MODE=false

# Função para exibir menu
show_menu() {
    echo -e "\n${CYAN}=== MENU DE CONTROLE ===${NC}"
    echo -e "${GREEN}1)${NC} Iniciar simulação"
    echo -e "${GREEN}2)${NC} Pausar/Retomar simulação"
    echo -e "${GREEN}3)${NC} Parar simulação"
    echo -e "${GREEN}4)${NC} Configurar parâmetros"
    echo -e "${GREEN}5)${NC} Mostrar configuração atual"
    echo -e "${GREEN}6)${NC} Testar conexão"
    echo -e "${GREEN}7)${NC} Mostrar status das portas"
    echo -e "${GREEN}8)${NC} Limpar arquivos temporários"
    echo -e "${GREEN}9)${NC} Controle avançado"
    echo -e "${GREEN}0)${NC} Sair"
    echo -e "${CYAN}========================${NC}"
}

# Função para configurar parâmetros
configure_parameters() {
    echo -e "\n${YELLOW}=== CONFIGURAÇÃO DE PARÂMETROS ===${NC}"
    
    echo -e "\n${BLUE}Configurações atuais:${NC}"
    echo "Oscilações: $OSCILLATION_COUNT"
    echo "Estabilidade: $STABILITY_COUNT"
    echo "Zero: $ZERO_COUNT"
    echo "Delay: $DELAY segundos"
    echo "Baudrate: $BAUDRATE"
    echo "Mandar apenas o peso: $WEIGHT_ONLY_MODE"
    
    echo -e "\n${YELLOW}Digite os novos valores (pressione Enter para manter o atual):${NC}"
    
    read -p "Número de oscilações [$OSCILLATION_COUNT]: " new_osc
    if [ ! -z "$new_osc" ]; then
        OSCILLATION_COUNT=$new_osc
    fi
    
    read -p "Número de estabilidade [$STABILITY_COUNT]: " new_stab
    if [ ! -z "$new_stab" ]; then
        STABILITY_COUNT=$new_stab
    fi
    
    read -p "Número de zero [$ZERO_COUNT]: " new_zero
    if [ ! -z "$new_zero" ]; then
        ZERO_COUNT=$new_zero
    fi
    
    read -p "Delay entre envios [$DELAY]: " new_delay
    if [ ! -z "$new_delay" ]; then
        DELAY=$new_delay
    fi
    
    read -p "Baudrate [$BAUDRATE]: " new_baud
    if [ ! -z "$new_baud" ]; then
        BAUDRATE=$new_baud
    fi

    read -p "Mandar apenas peso [$WEIGHT_ONLY_MODE]: " new_wom
    if [ ! -z "$new_wom" ]; then
        WEIGHT_ONLY_MODE=$new_wom
    fi
    
    echo -e "\n${GREEN}Configuração atualizada!${NC}"
}

# Função para mostrar configuração atual
show_config() {
    echo -e "\n${BLUE}=== CONFIGURAÇÃO ATUAL ===${NC}"
    echo "Porta da balança (envio): ${BALANCA_PORTS[0]}"
    echo "Porta da balança (config): ${BALANCA_PORTS[1]}"
    echo "Porta da impressora (envio): ${IMPRESSORA_PORTS[0]}"
    echo "Porta da impressora (monitor): ${IMPRESSORA_PORTS[1]}"
    echo "Oscilações: $OSCILLATION_COUNT"
    echo "Estabilidade: $STABILITY_COUNT"
    echo "Zero: $ZERO_COUNT"
    echo "Delay: $DELAY segundos"
    echo "Baudrate: $BAUDRATE"
    echo "Mandar apenas o peso: $WEIGHT_ONLY_MODE"
    echo "Status da simulação: $SIMULATION_STATUS"
}

# Função para testar conexão
test_connection() {
    echo -e "\n${YELLOW}Testando conexão com a porta ${BALANCA_PORTS[0]}...${NC}"
    if [ -e "${BALANCA_PORTS[0]}" ]; then
        echo -e "${GREEN}✓ Porta ${BALANCA_PORTS[0]} existe${NC}"
    else
        echo -e "${RED}✗ Porta ${BALANCA_PORTS[0]} não existe${NC}"
    fi
    
    if [ -e "${BALANCA_PORTS[1]}" ]; then
        echo -e "${GREEN}✓ Porta ${BALANCA_PORTS[1]} existe${NC}"
    else
        echo -e "${RED}✗ Porta ${BALANCA_PORTS[1]} não existe${NC}"
    fi
}

# Função para mostrar status das portas
show_port_status() {
    echo -e "\n${BLUE}=== STATUS DAS PORTAS ===${NC}"
    echo -e "${YELLOW}Portas da balança:${NC}"
    echo "  Envio: ${BALANCA_PORTS[0]}"
    echo "  Config: ${BALANCA_PORTS[1]}"
    echo -e "${YELLOW}Portas da impressora:${NC}"
    echo "  Envio: ${IMPRESSORA_PORTS[0]}"
    echo "  Monitor: ${IMPRESSORA_PORTS[1]}"
    
    echo -e "\n${YELLOW}Verificando existência das portas:${NC}"
    for port in "${BALANCA_PORTS[@]}" "${IMPRESSORA_PORTS[@]}"; do
        if [ -e "$port" ]; then
            echo -e "  ${GREEN}✓${NC} $port"
        else
            echo -e "  ${RED}✗${NC} $port"
        fi
    done
}

# Função para iniciar simulação
start_simulation() {
    if [ "$SIMULATION_RUNNING" = true ]; then
        echo -e "${YELLOW}Simulação já está rodando!${NC}"
        return
    fi
    
    echo -e "\n${GREEN}Iniciando simulação...${NC}"
    echo -e "${BLUE}Parâmetros:${NC}"
    echo "  Oscilações: $OSCILLATION_COUNT"
    echo "  Estabilidade: $STABILITY_COUNT"
    echo "  Zero: $ZERO_COUNT"
    echo "  Delay: $DELAY"
    echo "  Baudrate: $BAUDRATE"
    echo "  Mandar apenas o peso: $WEIGHT_ONLY_MODE"
    
    # Cria arquivo de configuração temporário para o Python
    cat > /tmp/simulation_config.json << EOF
{
    "oscillation_count": $OSCILLATION_COUNT,
    "stability_count": $STABILITY_COUNT,
    "zero_count": $ZERO_COUNT,
    "delay": $DELAY,
    "baudrate": $BAUDRATE,
    "port": "${BALANCA_PORTS[0]}",
    "weight_only_mode": "$WEIGHT_ONLY_MODE"
}
EOF
    
    # Inicia o script Python em background
    python tests/write_ports_interactive.py "${BALANCA_PORTS[0]}" &
    SIMULATION_PID=$!
    SIMULATION_RUNNING=true
    SIMULATION_STATUS="Rodando"
    
    echo -e "${GREEN}Simulação iniciada com PID: $SIMULATION_PID${NC}"
    echo -e "${YELLOW}Use a opção 2 para pausar/retomar${NC}"
}

# Função para parar simulação
stop_simulation() {
    if [ "$SIMULATION_RUNNING" = false ]; then
        echo -e "${YELLOW}Nenhuma simulação está rodando.${NC}"
        return
    fi
    
    echo -e "${YELLOW}Parando simulação...${NC}"
    
    # Cria arquivo de parada para o Python detectar
    touch /tmp/simulation_stop
    
    # Aguarda um pouco para o Python processar
    sleep 0.5
    
    # Mata o processo se ainda estiver rodando
    kill $SIMULATION_PID 2>/dev/null
    wait $SIMULATION_PID 2>/dev/null
    
    # Limpa arquivos de controle
    rm -f /tmp/simulation_stop /tmp/simulation_paused
    
    SIMULATION_RUNNING=false
    SIMULATION_PAUSED=false
    SIMULATION_STATUS="Parada"
    echo -e "${GREEN}Simulação parada!${NC}"
}

# Função para pausar/retomar simulação
pause_resume_simulation() {
    if [ "$SIMULATION_RUNNING" = false ]; then
        echo -e "${YELLOW}Nenhuma simulação está rodando.${NC}"
        return
    fi
    
    if [ "$SIMULATION_PAUSED" = true ]; then
        echo -e "${GREEN}Retomando simulação...${NC}"
        rm -f /tmp/simulation_paused
        SIMULATION_PAUSED=false
        SIMULATION_STATUS="Rodando"
    else
        echo -e "${YELLOW}Pausando simulação...${NC}"
        touch /tmp/simulation_paused
        SIMULATION_PAUSED=true
        SIMULATION_STATUS="Pausada"
    fi
}

# Função para limpar arquivos temporários
cleanup_temp_files() {
    echo -e "\n${YELLOW}Limpando arquivos temporários...${NC}"
    rm -f socat_balanca_output.txt socat_impressora_output.txt /tmp/simulation_config.json /tmp/simulation_stop /tmp/simulation_paused
    echo -e "${GREEN}Arquivos temporários removidos!${NC}"
}

# Inicialização
echo -e "${CYAN}=== SETUP DO AMBIENTE DE TESTE INTERATIVO ===${NC}"

# Cria o primeiro par de portas para a balança
echo -e "\n${BLUE}Criando portas virtuais para a balança...${NC}"
socat -d -d pty,raw,echo=0 pty,raw,echo=0 2> socat_balanca_output.txt &
BALANCA_SOCAT_PID=$!

# Aguarda um momento para garantir que o socat tenha gerado as portas
sleep 1

# Extrai as portas seriais da balança
BALANCA_PORTS=($(grep 'PTY is' socat_balanca_output.txt | awk '{print $NF}'))

# Verifica se as duas portas da balança foram capturadas
if [ ${#BALANCA_PORTS[@]} -ne 2 ]; then
    echo -e "${RED}Erro: Não foi possível capturar as duas portas seriais da balança.${NC}"
    exit 1
fi

# Cria o segundo par de portas para a impressora
echo -e "${BLUE}Criando portas virtuais para a impressora...${NC}"
socat -d -d pty,raw,echo=0 pty,raw,echo=0 2> socat_impressora_output.txt &
IMPRESSORA_SOCAT_PID=$!

# Aguarda um momento para garantir que o socat tenha gerado as portas
sleep 1

# Extrai as portas seriais da impressora
IMPRESSORA_PORTS=($(grep 'PTY is' socat_impressora_output.txt | awk '{print $NF}'))

# Verifica se as duas portas da impressora foram capturadas
if [ ${#IMPRESSORA_PORTS[@]} -ne 2 ]; then
    echo -e "${RED}Erro: Não foi possível capturar as duas portas seriais da impressora.${NC}"
    exit 1
fi

# Inicializa variáveis de controle
OSCILLATION_COUNT=$DEFAULT_OSCILLATION_COUNT
STABILITY_COUNT=$DEFAULT_STABILITY_COUNT
ZERO_COUNT=$DEFAULT_ZERO_COUNT
DELAY=$DEFAULT_DELAY
BAUDRATE=$DEFAULT_BAUDRATE
WEIGHT_ONLY_MODE=$DEFAULT_WEIGHT_ONLY_MODE
SIMULATION_RUNNING=false
SIMULATION_PAUSED=false
SIMULATION_STATUS="Parada"

# Atualiza automaticamente o campo 'usb_scaler' no arquivo de configuração
CONFIG_FILE="$HOME/.config/pesa_e_pronto/data/config.json"
if [ -f "$CONFIG_FILE" ]; then
    jq --arg port "${BALANCA_PORTS[1]}" '.usb_scaler = $port' "$CONFIG_FILE" > "${CONFIG_FILE}.tmp" && mv "${CONFIG_FILE}.tmp" "$CONFIG_FILE"
    echo -e "${GREEN}Campo 'usb_scaler' atualizado automaticamente em $CONFIG_FILE${NC}"
else
    echo -e "${YELLOW}Aviso: Arquivo de configuração $CONFIG_FILE não encontrado. Atualização automática não realizada.${NC}"
fi

# Define variável de ambiente para a impressora
export PRINTER_SERIAL_PORT=${IMPRESSORA_PORTS[0]}

echo -e "\n${GREEN}=== AMBIENTE CONFIGURADO COM SUCESSO ===${NC}"
echo -e "${BLUE}Portas da balança:${NC}"
echo "  Envio: ${BALANCA_PORTS[0]}"
echo "  Config: ${BALANCA_PORTS[1]}"
echo -e "${BLUE}Portas da impressora:${NC}"
echo "  Envio: ${IMPRESSORA_PORTS[0]}"
echo "  Monitor: ${IMPRESSORA_PORTS[1]}"
echo -e "${BLUE}Variável PRINTER_SERIAL_PORT:${NC} $PRINTER_SERIAL_PORT"

echo -e "\n${YELLOW}Para monitorar a impressora, execute em outro terminal:${NC}"
echo "  ./scripts/monitor_port.sh ${IMPRESSORA_PORTS[1]}"

# Loop principal do menu
while true; do
    show_menu
    read -p "Escolha uma opção: " choice
    
    case $choice in
        1)
            start_simulation
            ;;
        2)
            pause_resume_simulation
            ;;
        3)
            stop_simulation
            ;;
        4)
            configure_parameters
            ;;
        5)
            show_config
            ;;
        6)
            test_connection
            ;;
        7)
            show_port_status
            ;;
        8)
            cleanup_temp_files
            ;;
        9)
            # Chama o script de controle avançado
            if [ -f "scripts/advanced_simulation_control.sh" ]; then
                source scripts/advanced_simulation_control.sh
                main
            else
                echo -e "${RED}Script de controle avançado não encontrado!${NC}"
            fi
            ;;
        0)
            echo -e "\n${YELLOW}Encerrando...${NC}"
            if [ "$SIMULATION_RUNNING" = true ]; then
                echo -e "${YELLOW}Parando simulação...${NC}"
                stop_simulation
            fi
            echo -e "${YELLOW}Parando processos socat...${NC}"
            kill $BALANCA_SOCAT_PID $IMPRESSORA_SOCAT_PID 2>/dev/null
            cleanup_temp_files
            echo -e "${GREEN}Ambiente de teste encerrado!${NC}"
            exit 0
            ;;
        *)
            echo -e "${RED}Opção inválida!${NC}"
            ;;
    esac
    
    echo -e "\n${CYAN}Pressione Enter para continuar...${NC}"
    read
done
