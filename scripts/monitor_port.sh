#!/bin/bash

# Verifica se o argumento da porta foi fornecido
if [ -z "$1" ]; then
    echo "Erro: Forneça a porta da impressora como argumento (ex: /dev/pts/40)."
    exit 1
fi

# Verifica se a porta existe
if [ ! -e "$1" ]; then
    echo "Erro: A porta $1 não existe ou não está acessível."
    exit 1
fi

# Limpa a tela e monitora a porta da impressora continuamente
clear
echo "Monitorando a porta da impressora: $1"
echo "Pressione Ctrl+C para sair"
watch -n 4 cat "$1"
