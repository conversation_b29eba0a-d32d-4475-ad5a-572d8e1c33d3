# Sistema de Simulação Interativo - Pesa e Pronto

## Visão Geral

O sistema de simulação interativo foi desenvolvido para facilitar o desenvolvimento e teste do sistema Pesa e Pronto. Ele permite controlar em tempo real a simulação de uma balança e impressora, com configurações flexíveis e interface amigável.

## Arquivos Principais

- `setup_testing_environment.sh` - Script principal com menu interativo
- `advanced_simulation_control.sh` - Controles avançados de simulação
- `write_ports_interactive.py` - Script Python que executa a simulação
- `monitor_port.sh` - Monitor de porta serial (já existente)

## Como Usar

### 1. Iniciar o Ambiente

```bash
./scripts/setup_testing_environment.sh
```

O script irá:
- Criar portas virtuais para balança e impressora
- Configurar automaticamente o arquivo de configuração
- Exibir um menu interativo

### 2. <PERSON>u Principal

O menu principal oferece as seguintes opções:

```
=== MENU DE CONTROLE ===
1) Iniciar simulação
2) Pausar/Retomar simulação
3) Configurar parâmetros
4) Mostrar configuração atual
5) Testar conexão
6) Mostrar status das portas
7) Limpar arquivos temporários
8) Controle avançado
9) Sair
```

### 3. Configuração Básica (Opção 3)

Permite configurar:
- Número de oscilações
- Número de estabilidade
- Número de zero
- Delay entre envios
- Baudrate

### 4. Controle Avançado (Opção 8)

Oferece funcionalidades avançadas:

#### 4.1 Configurar Peso Específico
Define um peso específico para a fase de estabilidade.

#### 4.2 Configurar Padrão de Oscilação
Escolhe entre diferentes padrões:
- IIIIIIIIIIIIIIIIIIII (padrão padrão)
- 99999999999999999999 (nove)
- 00000000000000000000 (zero)
- Personalizado

#### 4.3 Configurar Tempo Entre Fases
Define delays específicos para cada fase.

#### 4.4 Executar Fase Única
Executa apenas uma fase específica (oscilação, estabilidade ou zero).

#### 4.5 Modos de Teste
- **Teste Rápido**: 3 oscilações, 2 estabilidade, 1 zero, delay 0.1s
- **Teste Lento**: 20 oscilações, 15 estabilidade, 5 zero, delay 1.0s

#### 4.6 Salvar/Carregar Configurações
Salva e carrega configurações personalizadas.

## Monitoramento

Para monitorar o que está sendo enviado para a impressora, execute em outro terminal:

```bash
./scripts/monitor_port.sh <porta_monitor>
```

A porta de monitor será exibida no menu principal.

## Configurações Padrão

- **Oscilações**: 10
- **Estabilidade**: 7
- **Zero**: 3
- **Delay**: 0.3 segundos
- **Baudrate**: 9600

## Estrutura de Dados

### Fase de Oscilação
Envia: `IIIIIIIIIIIIIIIIIIII`

### Fase de Estabilidade
Envia: `12345   000   67890`

### Fase de Zero
Envia: `00000   000   00000`

## Controles em Tempo Real

### Pausar/Retomar
- Use a opção 2 do menu principal
- A simulação pode ser pausada e retomada a qualquer momento

### Configuração Dinâmica
- As configurações podem ser alteradas sem parar a simulação
- O script Python recarrega as configurações automaticamente

## Arquivos Temporários

O sistema cria os seguintes arquivos temporários:
- `/tmp/simulation_config.json` - Configurações da simulação
- `/tmp/simulation_command.json` - Comandos para o Python
- `/tmp/simulation_paused` - Flag de pausa
- `socat_balanca_output.txt` - Log das portas da balança
- `socat_impressora_output.txt` - Log das portas da impressora

## Troubleshooting

### Portas não encontradas
- Verifique se o `socat` está instalado
- Execute a opção 6 para verificar o status das portas

### Simulação não inicia
- Verifique se o Python e `pyserial` estão instalados
- Use a opção 5 para testar a conexão

### Configuração não atualiza
- Verifique se o arquivo `/tmp/simulation_config.json` existe
- Use a opção 7 para limpar arquivos temporários

## Dependências

- `socat` - Para criar portas virtuais
- `jq` - Para manipular JSON
- `python` - Para executar a simulação
- `pyserial` - Para comunicação serial

## Exemplo de Uso Completo

```bash
# 1. Iniciar o ambiente
./scripts/setup_testing_environment.sh

# 2. Configurar parâmetros (opção 3)
# - Oscilações: 5
# - Estabilidade: 3
# - Zero: 2
# - Delay: 0.5

# 3. Iniciar simulação (opção 1)

# 4. Em outro terminal, monitorar a impressora
./scripts/monitor_port.sh /dev/pts/X

# 5. Pausar simulação (opção 2)

# 6. Configurar peso específico (opção 8 -> 1)
# - Peso: 5000

# 7. Retomar simulação (opção 2)

# 8. Sair (opção 9)
```

## Desenvolvimento

Para adicionar novas funcionalidades:

1. **Novos parâmetros**: Adicione no arquivo de configuração JSON
2. **Novas fases**: Modifique o script Python
3. **Novos controles**: Adicione opções no menu bash

O sistema é modular e extensível, permitindo fácil customização para diferentes cenários de teste. 