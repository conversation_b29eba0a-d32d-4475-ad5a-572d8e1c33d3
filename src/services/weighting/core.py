"""Módulo para o controlador de pesagem."""

from typing import List, Dict
import logging

from models import WeightingStatus, Request, Action, CommandPath

logger = logging.getLogger(__name__)


class Weighting:
    """
    Classe para gerenciar o estado de uma pesagem.

    Parameters
    ----------
    weight_unit : str, optional
        Unidade de peso. Padrão 'kg'.
    last_n_weights : int, optional
        Número de últimos pesos a considerar para verificação de estabilidade.
        Padrão é 5.
    """

    def __init__(
        self,
        threshold: int = 0,
        weight_unit: str = "kg",
        base_price: float | None = None
    ):
        self._weight_unit = weight_unit
        self._total_price = ""
        self._predefined_price = base_price is not None
        self._base_price = ""
        self._predefined_base_price = base_price or ""
        self.threshold = threshold + 1
        self.last_stable_weight = ""
        self.last_status = WeightingStatus.STABLE.value
        self.weights: List[str] = []
        self.family_mode: Dict[1, str] = {}
        self.active_family_mode = 0

    def __repr__(self) -> str:
        """Retorna uma representação oficial para desenvolvedores."""
        return (
            f"{self.__class__.__name__}("
            f"threshold={self._original_threshold!r}, "
            f"weight_unit={self._weight_unit!r}, "
            f"base_price={self._original_base_price!r}"
            f")"
        )

    def __str__(self) -> str:
        """Retorna uma representação legível do estado atual do objeto."""
        public_attrs = {
            key: value
            for key, value in self.__dict__.items()
            if not key.startswith('_')
        }

        attrs_str = "\n  ".join(
            f"{key}: {value!r}"
            for key, value in public_attrs.items()
        )

        return f"<{self.__class__.__name__} State>\n  {attrs_str}"

    def last_n_weights(self) -> str:
        """Getter para o número de pesos a considerar."""
        return self.weights[-self.threshold:]

    @property
    def weight_unit(self) -> str:
        """Getter para a unidade de peso."""
        return self._weight_unit

    @weight_unit.setter
    def weight_unit(self, value: str):
        if not value:
            raise ValueError("Unidade de peso não pode ser vazia.")
        self._weight_unit = value

    @property
    def total_price(self) -> str:
        """Getter para o preço total."""
        return self._total_price

    @total_price.setter
    def total_price(self, value: str):
        self._total_price = value

    @property
    def base_price(self) -> str:
        """Getter para o preço total."""
        return self._base_price

    @base_price.setter
    def base_price(self, value: str):
        self._base_price = value

    @property
    def predefined_base_price(self) -> str:
        """Getter para o preço total."""
        return self._predefined_base_price

    @predefined_base_price.setter
    def predefined_base_price(self, value: str):
        self._predefined_base_price = value

    def set_threshold(self, t):
        logger.info("Definindo novo threshold para a pesagem: %s", t)
        self.threshold = t + 1

    def set_predefined_price(self, val: bool):
        self._predefined_price = val

    def modify_based_on_request(self, request: Request) -> list:
        responses = []
        match request.path:
            case CommandPath.FAMILY_ONE_ON.value:
                if self.family_mode.get(1):
                    self.family_mode[1]["active"] = True
                    if len(self.family_mode[1]["weights"]) > 0:
                        responses.append({
                            "action": Action.COMMAND.value,
                            "path": CommandPath.FAMILY_ONE_STANDBY_OFF.value
                        })
                else:
                    self.family_mode[1] = {
                        "active": True,
                        "weights": []
                    }

                if self.family_mode.get(2):
                    if len(self.family_mode[2]["weights"]) == 0:
                        del self.family_mode[2]
                        return responses

                    self.family_mode[2]["active"] = False
                    responses.append({
                        "action": Action.COMMAND.value,
                        "path": CommandPath.FAMILY_TWO_STANDBY_ON.value
                    })

                self.active_family_mode = 1

            case CommandPath.FAMILY_TWO_ON.value:
                if self.family_mode.get(2):
                    self.family_mode[2]["active"] = True
                    if len(self.family_mode[2]["weights"]) > 0:
                        responses.append({
                            "action": Action.COMMAND.value,
                            "path": CommandPath.FAMILY_TWO_STANDBY_OFF.value
                        })
                else:
                    self.family_mode[2] = {
                        "active": True,
                        "weights": []
                    }

                if self.family_mode.get(1):
                    if len(self.family_mode[1]["weights"]) == 0:
                        del self.family_mode[1]
                        return responses

                    self.family_mode[1]["active"] = False
                    responses.append({
                        "action": Action.COMMAND.value,
                        "path": CommandPath.FAMILY_ONE_STANDBY_ON.value
                    })

                self.active_family_mode = 2

            case CommandPath.FAMILY_ONE_OFF.value:
                if self.family_mode.get(1):
                    if len(self.family_mode[1]["weights"]) == 0:
                        del self.family_mode[1]
                    else:
                        self.family_mode[1]["active"] = False
                        responses.append({
                            "action": Action.COMMAND.value,
                            "path": CommandPath.FAMILY_ONE_STANDBY_ON.value
                        })

                if self.active_family_mode == 1:
                    self.active_family_mode = 0

            case CommandPath.FAMILY_TWO_OFF.value:
                if self.family_mode.get(2):
                    if len(self.family_mode[2]["weights"]) == 0:
                        del self.family_mode[2]
                    else:
                        self.family_mode[2]["active"] = False
                        responses.append({
                            "action": Action.COMMAND.value,
                            "path": CommandPath.FAMILY_TWO_STANDBY_ON.value
                        })

                if self.active_family_mode == 2:
                    self.active_family_mode = 0

        return responses

    def announce_state(self) -> list[dict]:
        """
        Gera uma série de mensagens para sincronizar o front com o estado atual
        """
        # TODO

    def clear_family_mode(self, mode: int):
        try:
            del self.family_mode[mode]
            if self.active_family_mode == mode:
                self.active_family_mode = 0
        except KeyError:
            pass

    def add_weight(self, weight: str) -> str:
        """
        Adiciona um novo peso à lista de pesos.

        Parameters
        ----------
        weight : str
            O peso a ser adicionado.

        Returns
        -------
        str
            O peso adicionado.
        """
        if "I" in weight or "N" in weight:
            weight, base_price, total_price = "IIIII", "00000", "00000"
        else:
            weight = weight.split()
            if len(weight) < 3:
                self.weights.append(weight[0])
                # Se for detectado peso sozinho, já tenta usar peso predefinido
                self.set_predefined_price(True)
                self.base_price = self.predefined_base_price
                return weight
            # Se tiver o que precisa, usa o da balança
            self.set_predefined_price(False)
            weight, base_price, total_price = weight[0], weight[1], weight[2]
        self.weights.append(weight)
        self.total_price = total_price.strip()
        self.base_price = base_price.strip()
        return weight

    def _process_price(self, price: str) -> str:
        if len(price) <= 4:
            price = "0" * (5 - len(price)) + price
        elif len(price) == 1:
            price = "00000"

        price = price[:3] + "." + price[3:]

        if price.startswith("0"):
            price = price.removeprefix("0")

        return price

    def current_state(self) -> dict:
        """
        Retorna o estado atual da pesagem.

        A estabilidade é determinada analisando a relação do peso atual com os últimos
        `last_n_weights` valores adicionados. Se todos estiverem dentro do limiar `threshold`
        em relação ao peso atual, o estado é considerado "stable". Caso contrário, "stabilizing".

        Returns
        -------
        dict
            Dicionário com o estado atual da pesagem, contendo:
            - **peso** (str): Peso atual.
            - **preco** (str): Preço calculado com base no peso atual e no preço por unidade.
            - **preco_kg** (str): Preço por Kg.
            - **status** (str): "stable" se todos os últimos `last_n_weights` pesos estiverem
            dentro do limiar `threshold` do peso atual, caso contrário "stabilizing".
        """

        if len(self.weights) > 100:
            self.clear_weights()

        current_weight = self.weights[-1]
        try:
            last_weights = self.last_n_weights()
        except IndexError:
            logger.error("Erro ao obter os últimos pesos.")
            last_weights = self.weights

        logger.info(
            "Pesos sendo considerados para a estabilidade: %s",
            len(last_weights)
        )

        status = WeightingStatus.STABLE.value

        stable_weights = [weight for weight in last_weights if current_weight == weight]

        ##### Verificação do status da pesagem #####
        # Se aparecer “IIIII” em last_weights, continua estabilizando:
        if len(stable_weights) < self.threshold or ("IIIII" in last_weights):
            status = WeightingStatus.STABILIZING.value
        # Se houver “00000”, fica WAITING:
        elif "00000" in last_weights:
            status = WeightingStatus.WAITING.value
            # Atualiza último peso estável para quando zerar a balança, aceitar novo peso
            self.last_stable_weight = 0
        else:
            # **NOVA PARTE**: tolerância numérica de ±1
            try:
                last_int = int(self.last_stable_weight)
                curr_int = int(current_weight)
            except (ValueError, TypeError):
                # Se não der pra converter, cai na comparação exata
                last_int = None
                curr_int = None

            if (
                last_int is not None
                and curr_int is not None
                and abs(curr_int - last_int) <= 1
            ):
                # Pequena variação: continua THANKS
                status = WeightingStatus.THANKS.value
            elif self.last_stable_weight == current_weight:
                status = WeightingStatus.THANKS.value
            else:
                # Aqui: peso diferente além da tolerância
                self.last_stable_weight = ""

            if status == WeightingStatus.STABLE.value:
                self.last_stable_weight = current_weight

        ##### Processamento do peso e do preço #####
        if current_weight == "IIIII":
            current_weight = "00000"

        weight = current_weight[:2] + "." + current_weight[2:]

        if not self._predefined_price:
            total_price = self._process_price(self.total_price)
            base_price = self._process_price(self.base_price)
        else:
            total_price = f"{float(self.base_price) * float(weight):.3f}"
            base_price = f"{float(self.base_price):.3f}"

        logger.info(total_price)
        logger.info(weight)

        self.last_status = status

        return {
            "peso": weight,
            "preco": total_price,
            "preco_kg": base_price,
            "status": status,
        }

    def clear_weights(self):
        """
        Limpa a lista de pesos.
        """
        if len(self.weights) >= self.threshold:
            self.weights.clear()
