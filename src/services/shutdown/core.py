"""Módulo para gerenciar shutdown graceful do sistema."""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional

from utils.handle_path import get_config_path

logger = logging.getLogger(__name__)


def get_client_name() -> Optional[str]:
    """
    Obtém o nome do cliente a partir da configuração.

    Returns:
        str: Nome do cliente ou None se não encontrado
    """
    try:
        # Importação local para evitar dependências circulares
        from services.files.core import Files

        files_service = Files()
        config_client = files_service.get("config_client")

        if config_client and config_client.get("status") == "success":
            client_data = config_client.get("data", {})
            nome_cliente = client_data.get("nome_cliente")
            nome_restaurante = client_data.get("nome_restaurante")

            # Prioriza nome_cliente, mas usa nome_restaurante como fallback
            return nome_cliente or nome_restaurante

        return None

    except Exception as e:
        logger.error("Erro ao obter nome do cliente: %s", e)
        return None


class ShutdownService:
    """
    Serviço para gerenciar shutdown graceful do sistema.

    Este serviço:
    - Registra quando o sistema inicia
    - Salva dados de controle quando o shutdown é iniciado via comando
    - Verifica se o shutdown anterior foi correto
    - Emite warnings quando detecta shutdown incorreto
    """

    def __init__(self):
        self.config_path = get_config_path()
        self.data_dir = os.path.join(self.config_path, "data")
        self.shutdown_file = os.path.join(self.data_dir, "system_shutdown.json")

        # Garante que o diretório existe
        os.makedirs(self.data_dir, exist_ok=True)



    def register_startup(self) -> None:
        """Registra que o sistema foi iniciado e verifica shutdown anterior."""
        try:
            logger.info("Sistema iniciado: %s", datetime.now().isoformat())

            # Verifica se o shutdown anterior foi correto
            self._check_previous_shutdown()

        except Exception as e:
            logger.error("Erro ao registrar startup: %s", e)

    def register_shutdown(self) -> None:
        """Registra que o sistema está sendo desligado."""
        try:
            shutdown_data = {
                "timestamp": datetime.now().isoformat(),
                "pid": os.getpid(),
                "status": "shutdown_initiated"
            }

            with open(self.shutdown_file, "w", encoding="utf-8") as f:
                json.dump(shutdown_data, f, indent=2)

            logger.info("Shutdown registrado: %s", shutdown_data["timestamp"])

        except Exception as e:
            logger.error("Erro ao registrar shutdown: %s", e)

    def _check_previous_shutdown(self) -> None:
        """Verifica se o shutdown anterior foi correto."""
        try:
            # Verifica se existe arquivo de shutdown
            if not os.path.exists(self.shutdown_file):
                logger.info("Nenhum registro de shutdown anterior - primeira execução ou tudo ok")
                return

            # Lê dados do shutdown anterior
            with open(self.shutdown_file, "r", encoding="utf-8") as f:
                shutdown_data = json.load(f)

            status = shutdown_data.get("status", "")
            timestamp = shutdown_data.get("timestamp", "desconhecido")

            if status == "started":
                # Sistema foi ligado mas não desligado corretamente
                logger.warning(
                    "Sistema não foi desligado corretamente! Último registro: %s", timestamp
                )
                # Envia notificação para o Slack
                self._send_slack_notification()
            elif status == "shutdown_initiated":
                # Sistema foi desligado corretamente
                logger.info("Shutdown anterior foi correto: %s", timestamp)
            else:
                logger.warning("Status de shutdown desconhecido: %s", status)

            # Sempre apaga o arquivo de shutdown após verificação
            os.remove(self.shutdown_file)
            logger.info("Arquivo de shutdown anterior removido")

        except json.JSONDecodeError as e:
            logger.error("Erro ao decodificar arquivo de shutdown: %s", e)
        except Exception as e:
            logger.error("Erro ao verificar shutdown anterior: %s", e)



    def get_system_status(self) -> Dict[str, Any]:
        """Retorna o status atual do sistema."""
        try:
            status = {
                "shutdown_file_exists": os.path.exists(self.shutdown_file),
                "current_pid": os.getpid()
            }

            if status["shutdown_file_exists"]:
                with open(self.shutdown_file, "r", encoding="utf-8") as f:
                    status["current_shutdown_data"] = json.load(f)

            return status

        except Exception as e:
            logger.error("Erro ao obter status do sistema: %s", e)
            return {"error": str(e)}

    def cleanup_old_files(self) -> None:
        """Remove arquivo de controle de shutdown."""
        try:
            if os.path.exists(self.shutdown_file):
                os.remove(self.shutdown_file)
                logger.info("Arquivo de shutdown removido")
            else:
                logger.info("Nenhum arquivo de shutdown para remover")

        except Exception as e:
            logger.error("Erro ao limpar arquivo de shutdown: %s", e)

    def _send_slack_notification(self) -> None:
        """Envia notificação de shutdown incorreto para o Slack."""
        try:
            # Importação local para evitar dependências circulares
            from services.notifications.slack import SlackNotificationService

            # Obtém o nome do cliente
            nome_cliente = get_client_name()

            if not nome_cliente:
                logger.warning(
                    "Nome do cliente não encontrado. Usando nome padrão para notificação."
                )
                nome_cliente = "Cliente Desconhecido"

            # Envia notificação
            slack_service = SlackNotificationService()
            success = slack_service.send_improper_shutdown_notification(nome_cliente)

            if success:
                logger.info(
                    "Notificação de shutdown incorreto enviada para o Slack: %s",
                    nome_cliente
                )
            else:
                logger.error(
                    "Falha ao enviar notificação de shutdown incorreto para o Slack"
                )

        except Exception as e:
            logger.error("Erro ao enviar notificação para o Slack: %s", e)

    def force_exit(self) -> None:
        """
        Força a saída da aplicação de forma mais robusta.

        Tenta fazer cleanup de recursos importantes e depois força a saída,
        mesmo com asyncio, WebSocket servers e tarefas em background rodando.
        """
        import threading
        import time

        def force_exit_thread():
            """Thread separada para forçar saída após timeout."""
            time.sleep(2)  # Aguarda 2 segundos para cleanup
            logger.warning("Forçando saída da aplicação com os._exit(0)")
            os._exit(0)

        try:
            logger.info("Iniciando shutdown forçado da aplicação...")

            # Inicia thread de backup para forçar saída
            exit_thread = threading.Thread(target=force_exit_thread, daemon=True)
            exit_thread.start()

            # Tenta cleanup básico primeiro
            try:
                # Para o loop asyncio se estiver rodando
                import asyncio
                try:
                    loop = asyncio.get_running_loop()
                    if loop and not loop.is_closed():
                        logger.info("Parando loop asyncio...")
                        loop.stop()
                except RuntimeError:
                    # Não há loop rodando
                    pass
            except Exception as e:
                logger.error("Erro ao parar loop asyncio: %s", e)

            # Tenta fechar conexões seriais
            try:
                from controllers.serial.serial_connection import SerialConnection
                SerialConnection.disconnect()
                logger.info("Conexões seriais fechadas")
            except Exception as e:
                logger.error("Erro ao fechar conexões seriais: %s", e)

            logger.info("Cleanup básico concluído, forçando saída...")

            # Força saída imediata
            os._exit(0)

        except Exception as e:
            logger.error("Erro durante shutdown forçado: %s", e)
            # Última tentativa - força saída diretamente
            os._exit(1)
