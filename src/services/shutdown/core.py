"""Módulo para gerenciar shutdown graceful do sistema."""

import os
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional

from utils.handle_path import get_config_path

logger = logging.getLogger(__name__)


def get_client_name() -> Optional[str]:
    """
    Obtém o nome do cliente a partir da configuração.

    Returns:
        str: Nome do cliente ou None se não encontrado
    """
    try:
        # Importação local para evitar dependências circulares
        from services.files.core import Files

        files_service = Files()
        config_client = files_service.get("config_client")

        if config_client and config_client.get("status") == "success":
            client_data = config_client.get("data", {})
            nome_cliente = client_data.get("nome_cliente")
            nome_restaurante = client_data.get("nome_restaurante")

            # Prioriza nome_cliente, mas usa nome_restaurante como fallback
            return nome_cliente or nome_restaurante

        return None

    except Exception as e:
        logger.error("Erro ao obter nome do cliente: %s", e)
        return None


class ShutdownService:
    """
    Serviço para gerenciar shutdown graceful do sistema.

    Este serviço:
    - Registra quando o sistema inicia
    - Salva dados de controle quando o shutdown é iniciado via comando
    - Verifica se o shutdown anterior foi correto
    - Emite warnings quando detecta shutdown incorreto
    """

    def __init__(self):
        self.config_path = get_config_path()
        self.data_dir = os.path.join(self.config_path, "data")
        self.startup_file = os.path.join(self.data_dir, "system_startup.json")
        self.shutdown_file = os.path.join(self.data_dir, "system_shutdown.json")

        # Garante que o diretório existe
        os.makedirs(self.data_dir, exist_ok=True)



    def register_startup(self) -> None:
        """Registra que o sistema foi iniciado."""
        try:
            startup_data = {
                "timestamp": datetime.now().isoformat(),
                "pid": os.getpid(),
                "status": "started"
            }

            with open(self.startup_file, "w", encoding="utf-8") as f:
                json.dump(startup_data, f, indent=2)

            logger.info("Sistema iniciado registrado: %s", startup_data["timestamp"])

            # Verifica se o shutdown anterior foi correto
            self._check_previous_shutdown()

        except Exception as e:
            logger.error("Erro ao registrar startup: %s", e)

    def register_shutdown(self) -> None:
        """Registra que o sistema está sendo desligado."""
        try:
            shutdown_data = {
                "timestamp": datetime.now().isoformat(),
                "pid": os.getpid(),
                "status": "shutdown_initiated"
            }

            with open(self.shutdown_file, "w", encoding="utf-8") as f:
                json.dump(shutdown_data, f, indent=2)

            logger.info("Shutdown registrado: %s", shutdown_data["timestamp"])

        except Exception as e:
            logger.error("Erro ao registrar shutdown: %s", e)

    def _check_previous_shutdown(self) -> None:
        """Verifica se o shutdown anterior foi correto."""
        try:
            # Verifica se existe arquivo de startup anterior
            if not os.path.exists(self.startup_file):
                logger.info("Primeira execução do sistema detectada")
                return

            # Lê dados do startup anterior
            with open(self.startup_file, "r", encoding="utf-8") as f:
                previous_startup = json.load(f)

            # Verifica se existe arquivo de shutdown correspondente
            if not os.path.exists(self.shutdown_file):
                logger.warning(
                    "Sistema não foi desligado corretamente! "
                    "Último startup: %s, PID: %s. "
                    "Não foi encontrado registro de shutdown.",
                    previous_startup.get("timestamp", "desconhecido"),
                    previous_startup.get("pid", "desconhecido")
                )

                # Envia notificação para o Slack
                self._send_slack_notification()
                return

            # Lê dados do shutdown anterior
            with open(self.shutdown_file, "r", encoding="utf-8") as f:
                previous_shutdown = json.load(f)

            # Compara timestamps e PIDs
            startup_timestamp = previous_startup.get("timestamp")
            shutdown_timestamp = previous_shutdown.get("timestamp")
            startup_pid = previous_startup.get("pid")
            shutdown_pid = previous_shutdown.get("pid")

            if startup_pid != shutdown_pid:
                logger.warning(
                    "Sistema não foi desligado corretamente! "
                    "PID do startup (%s) diferente do PID do shutdown (%s). "
                    "Startup: %s, Shutdown: %s",
                    startup_pid, shutdown_pid,
                    startup_timestamp, shutdown_timestamp
                )

                # Envia notificação para o Slack
                self._send_slack_notification()
            else:
                logger.info(
                    "Shutdown anterior foi correto. "
                    "Startup: %s, Shutdown: %s",
                    startup_timestamp, shutdown_timestamp
                )

        except json.JSONDecodeError as e:
            logger.error("Erro ao decodificar arquivos de controle: %s", e)
        except Exception as e:
            logger.error("Erro ao verificar shutdown anterior: %s", e)



    def get_system_status(self) -> Dict[str, Any]:
        """Retorna o status atual do sistema."""
        try:
            status = {
                "startup_file_exists": os.path.exists(self.startup_file),
                "shutdown_file_exists": os.path.exists(self.shutdown_file),
                "current_pid": os.getpid()
            }

            if status["startup_file_exists"]:
                with open(self.startup_file, "r", encoding="utf-8") as f:
                    status["last_startup"] = json.load(f)

            if status["shutdown_file_exists"]:
                with open(self.shutdown_file, "r", encoding="utf-8") as f:
                    status["last_shutdown"] = json.load(f)

            return status

        except Exception as e:
            logger.error("Erro ao obter status do sistema: %s", e)
            return {"error": str(e)}

    def cleanup_old_files(self) -> None:
        """Remove arquivos antigos de controle."""
        try:
            files_removed = []

            if os.path.exists(self.startup_file):
                os.remove(self.startup_file)
                files_removed.append("startup")

            if os.path.exists(self.shutdown_file):
                os.remove(self.shutdown_file)
                files_removed.append("shutdown")

            if files_removed:
                logger.info(
                    "Arquivos de controle removidos: %s", ", ".join(files_removed)
                )
            else:
                logger.info("Nenhum arquivo de controle para remover")

        except Exception as e:
            logger.error("Erro ao limpar arquivos antigos: %s", e)

    def _send_slack_notification(self) -> None:
        """Envia notificação de shutdown incorreto para o Slack."""
        try:
            # Importação local para evitar dependências circulares
            from services.notifications.slack import SlackNotificationService

            # Obtém o nome do cliente
            nome_cliente = get_client_name()

            if not nome_cliente:
                logger.warning(
                    "Nome do cliente não encontrado. Usando nome padrão para notificação."
                )
                nome_cliente = "Cliente Desconhecido"

            # Envia notificação
            slack_service = SlackNotificationService()
            success = slack_service.send_improper_shutdown_notification(nome_cliente)

            if success:
                logger.info(
                    "Notificação de shutdown incorreto enviada para o Slack: %s",
                    nome_cliente
                )
            else:
                logger.error(
                    "Falha ao enviar notificação de shutdown incorreto para o Slack"
                )

        except Exception as e:
            logger.error("Erro ao enviar notificação para o Slack: %s", e)
