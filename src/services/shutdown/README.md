# ShutdownService

Serviço para gerenciar shutdown graceful do sistema Pesa e Pronto.

## Funcionalidades

- **Registro de Startup**: Registra quando o sistema é iniciado
- **Registro de Shutdown**: Registra quando o sistema é desligado via comando
- **Detecção de Shutdown Incorreto**: Verifica se o shutdown anterior foi correto
- **Logs de Warning**: Emite warnings quando detecta shutdown incorreto
- **Notificações Slack**: Envia notificações para Slack quando detecta shutdown incorreto
- **Poweroff Inteligente**: Tenta poweroff sem sudo, registra como graceful mesmo se falhar

## Como Usar

### Inicialização

```python
from services.shutdown.core import ShutdownService

# Inicializa o service (já registra handlers de sinal)
shutdown_service = ShutdownService()

# Registra que o sistema foi iniciado
shutdown_service.register_startup()
```

### Shutdown Manual

```python
# Registra shutdown antes de desligar
shutdown_service.register_shutdown()

# Tenta poweroff sem sudo primeiro
try:
    result = os.system("poweroff")
    if result != 0:
        logger.warning("Poweroff sem sudo falhou, mas shutdown foi registrado como graceful")
except Exception as e:
    logger.error("Erro ao executar poweroff: %s", e)
    logger.info("Shutdown registrado como graceful mesmo com erro no poweroff")
```

### Verificação de Status

```python
# Obtém status atual do sistema
status = shutdown_service.get_system_status()
print(status)
```

### Limpeza de Arquivos

```python
# Remove arquivos de controle antigos
shutdown_service.cleanup_old_files()
```

## Arquivo de Controle

O service usa apenas um arquivo de controle em `~/.config/pesa_e_pronto/data/`:

- `system_shutdown.json`: Controla o estado do sistema

### Formato do Arquivo

```json
{
  "timestamp": "2025-08-13T22:39:22.564634",
  "status": "started" // ou "shutdown_initiated"
}
```

### Estados Possíveis

- **"started"**: Sistema foi ligado
- **"shutdown_initiated"**: Sistema foi desligado via comando

## Detecção de Shutdown Incorreto

A lógica foi simplificada para usar apenas um arquivo de controle:

1. **Ao ligar**: Verifica se existe arquivo de shutdown
   - **Não existe** → primeira execução ou tudo ok, não faz nada
   - **Existe com status="started"** → não desligou corretamente, emite warning
   - **Existe com status="shutdown_initiated"** → desligou corretamente, tudo ok
   - **Sempre remove** o arquivo após verificação

2. **Ao ligar**: Cria novo arquivo com status="started"

3. **Ao desligar**: Atualiza arquivo com status="shutdown_initiated"

Quando detecta shutdown incorreto, emite `logger.warning` e envia notificação para Slack.

## Integração com o Sistema

### No Entrypoint

```python
# src/entrypoint.py
from services.shutdown.core import ShutdownService

# Inicializa o service de shutdown
shutdown_service = ShutdownService()
shutdown_service.register_startup()
```

### No Comando de Shutdown

```python
# src/controllers/socket/tasks.py
elif request.path == "shutdown_computer":
    logger.info("Desligando computador!!!")
    # Registra o shutdown antes de desligar
    from services.shutdown.core import ShutdownService
    shutdown_service = ShutdownService()
    shutdown_service.register_shutdown()

    # Tenta poweroff sem sudo primeiro
    try:
        logger.info("Tentando poweroff sem sudo...")
        result = os.system("poweroff")
        if result != 0:
            logger.warning("Poweroff sem sudo falhou, mas shutdown foi registrado como graceful")
    except Exception as e:
        logger.error("Erro ao executar poweroff: %s", e)
        logger.info("Shutdown registrado como graceful mesmo com erro no poweroff")
```

## Comando de Shutdown

O service funciona apenas com shutdown manual via comando:

- **Comando**: `shutdown_computer` via WebSocket
- **Comportamento**: Registra shutdown graceful e tenta poweroff
- **Fallback**: Se poweroff falhar, ainda considera como graceful shutdown

## Logs

O service emite os seguintes tipos de log:

- **INFO**: Startup registrado, shutdown registrado, shutdown anterior correto
- **WARNING**: Sistema não foi desligado corretamente
- **ERROR**: Erros ao ler/escrever arquivos de controle

## Integração com Slack

O service automaticamente envia notificações para o Slack quando detecta shutdown incorreto.

### Webhook do Slack

URL configurada: `https://hooks.slack.com/triggers/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`

### Payload Enviado

```json
{
  "nome_cliente": "Nome do Cliente"
}
```

### Obtenção do Nome do Cliente

O service obtém o nome do cliente da configuração:

1. **Prioridade 1**: `config_client.nome_cliente`
2. **Prioridade 2**: `config_client.nome_restaurante` (fallback)
3. **Prioridade 3**: "Cliente Desconhecido" (se nenhum encontrado)

### Configuração do Cliente

Para que as notificações incluam o nome correto:

```json
{
  "config_client": {
    "nome_cliente": "João Silva",
    "nome_restaurante": "Restaurante do João"
  }
}
```

## Exemplo de Uso Completo

```python
import logging
from services.shutdown.core import ShutdownService

# Configura logging
logging.basicConfig(level=logging.INFO)

# Inicializa o service (já integrado com Slack)
shutdown_service = ShutdownService()

# Registra startup (verifica shutdown anterior e envia notificação se necessário)
shutdown_service.register_startup()

# ... código da aplicação ...

# Ao receber comando de shutdown
shutdown_service.register_shutdown()
os.system("poweroff")
```

## Testes

Execute o script de teste para verificar o funcionamento:

```bash
python test_shutdown_service.py
```

O teste verifica:
- Startup e shutdown normal
- Detecção de shutdown incorreto
- Limpeza de arquivos
- Status do sistema
