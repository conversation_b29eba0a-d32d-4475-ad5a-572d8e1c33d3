"""Módulo para gerenciar operações de arquivos JSON."""

import os
import json
import logging
import platform
import sys
from typing import Any, Dict, Optional, List

import usb.core
from pydantic import BaseModel
from serial.tools import list_ports
from serial import SerialException
import usb.backend.libusb1

from models import Request
import models as models
from controllers.serial.serial_connection import SerialConnection
from services.report.record import get_today_data_as_json
from utils.handle_path import get_config_path


def get_libusb_path():
    system = platform.system()

    if system == "Darwin":  # macOS
        path = "/opt/homebrew/opt/libusb/lib/libusb-1.0.dylib"
    elif system == "Linux":
        path = "/usr/lib/aarch64-linux-gnu/libusb-1.0.so"  # comum no Ubuntu
        if not os.path.exists(path):
            path = "/usr/lib/libusb-1.0.so"  # fallback
    else:
        print(f"Sistema operacional não suportado: {system}")
        sys.exit(1)

    if not os.path.exists(path):
        print(f"Arquivo libusb não encontrado: {path}")
        sys.exit(1)

    return path


# Configura o caminho da libusb de acordo com o sistema
os.environ["LIBUSB_LIBRARY_PATH"] = get_libusb_path()

backend = usb.backend.libusb1.get_backend()
if backend is None:
    print("Erro: Nenhum backend PyUSB disponível!")
    sys.exit(1)

logger = logging.getLogger(__name__)


class Files:
    """
    Serviço para gerenciar operações de arquivos JSON.
    """

    CONFIG_PATH = f"{get_config_path()}/data/config.json"

    def __init__(self):
        if not os.path.exists(f"{get_config_path()}/data"):
            os.makedirs(f"{get_config_path()}/data")
        if not os.path.exists(self.CONFIG_PATH):
            with open(self.CONFIG_PATH, "w", encoding="utf-8") as f:
                json.dump({}, f, indent=4)

    def request(self, request: Request) -> Any:
        """
        Roteia a chamada para o método correto.

        Parameters
        ----------
        action : str
            Método a ser chamado ('POST', 'GET', 'DELETE', 'PUT').
        kwargs : dict
            Argumentos do método.

        Returns
        -------
        Any
            Retorno do método chamado.
        """
        try:
            method = getattr(self, request.action.lower())
            return method(request.path, request.data)
        except AttributeError:
            logger.error("Ação '%s' não suportada.", request.action)
            return {
                "status": "error",
                "message": f"Ação '{request.action}' não suportada.",
            }

    def _read_config(self) -> Dict[str, Any]:
        """
        Lê o arquivo de configuração JSON.

        Returns
        -------
        Dict[str, Any]
            Dados do arquivo de configuração.
        """
        try:
            with open(self.CONFIG_PATH, "r", encoding="utf-8") as f:
                config = json.load(f)
            return config
        except json.JSONDecodeError as e:
            logger.error("Erro ao decodificar JSON: %s", e)
            return {}
        except Exception as e:  # pylint: disable=broad-except
            logger.error("Erro ao ler o arquivo de configuração: %s", e)
            return {}

    def _write_config(self, config: Dict[str, Any]) -> None:
        """
        Escreve dados no arquivo de configuração JSON.

        Parameters
        ----------
        config : Dict[str, Any]
            Dados a serem escritos.
        """
        try:
            with open(self.CONFIG_PATH, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=4)
        except Exception as e:  # pylint: disable=broad-except
            logger.error("Erro ao escrever no arquivo de configuração: %s", e)

    def post(self, path: str, data: Any) -> Dict[str, Any]:
        """
        Escreve dados no caminho especificado.

        Parameters
        ----------
        path : str
            Caminho do arquivo ('products', 'usb_printer', etc.).
        data : Any
            Dados a serem escritos.

        Returns
        -------
        Dict[str, Any]
            Resultado da operação.
        """
        try:
            config = self._read_config()

            # Estes são recursos de lista
            if path in (
                "product", "email", "weight_by_software"
            ):
                if isinstance(data, str):
                    data = json.loads(data)

                if not isinstance(config.get(path), dict):
                    logger.info("Criando novo dicionário para '%s'.", path)
                    config[path] = {}

                if isinstance(data, list):
                    if len(data) == 0:
                        logger.warning("Lista de %s vazia.", path)
                        return {
                            "status": "warning",
                            "message": f"Lista de {path} vazia.",
                            "action": "POST",
                            "path": path,
                        }
                    for item in data:
                        config = self.add_item(path, item, config)

                elif isinstance(data, dict):
                    config = self.add_item(path, data, config)
                else:
                    logger.error(
                        "Dados inválidos para '%s'. Deve ser uma lista ou dicionário.",
                        path,
                    )
                    return {
                        "status": "error",
                        "message": f"Dados inválidos para '{path}'.",
                        "action": "POST",
                        "path": path,
                    }

            elif path == "usb_devices":
                usb_scaler = data.get("usb_scaler", None)
                usb_printer = data.get("usb_printer", None)
                if usb_scaler:
                    try:
                        config["usb_scaler"] = usb_scaler
                        conn = SerialConnection()
                        conn.disconnect()
                        baudrate = int(os.getenv("STD_BAUDRATE", "2400"))
                        timeout = int(os.getenv("STD_TIMEOUT", "1"))
                        conn.connect(port=usb_scaler, baudrate=baudrate, timeout=timeout)
                        logger.info(
                            "Dispositivo USB para balança atualizado: %s", usb_scaler
                        )
                    except SerialException:
                        logger.error("A balança informada é inválida!")
                if usb_printer:
                    config["usb_printer"] = usb_printer
                    logger.info(
                        "Dispositivo USB para impressora atualizado: %s", usb_printer
                    )
            elif path == "email":
                config["emails"] = data
            else:
                # Isso garante persistencia de dados antigos
                # em dados genéricos de tipo dict
                if isinstance(data, dict):
                    old_data = config.get(path, {})

                    if not isinstance(old_data, dict):
                        old_data = {}
                    new_data_keys = data.keys()
                    for key, value in old_data.items():
                        if key not in new_data_keys:
                            data[key] = value

                config[path] = data
                logger.info("Dado para '%s' atualizado.", path)
            logger.info("Escrevendo dados em '%s'.", path)
            self._write_config(config)
            return {
                "status": "success",
                "message": "Dados escritos com sucesso.",
                "data": data,
                "action": "POST",
                "path": path,
            }
        except Exception as e:  # pylint: disable=broad-except
            import traceback  # pylint: disable=import-outside-toplevel

            logger.error("Erro no método POST: %s", e)
            logger.exception(traceback.format_exc())
            return {
                "status": "error",
                "message": f"Erro no método POST: {e}",
                "action": "POST",
                "path": path,
            }

    def get(
        self, path: str, *kwargs, pure: bool = False
    ) -> Dict[str, Any]:  # pylint: disable=unused-argument
        """
        Lê dados do caminho especificado.

        Parameters
        ----------
        path : str
            Caminho do arquivo ('products', 'usb_printer', etc.).

        Returns
        -------
        Dict[str, Any]
            Dados lidos ou um erro.
        """
        try:
            config = self._read_config()

            logger.info("Lendo dados de '%s'.", path)

            identifier = path.split("/")[-1] if "/" in path else None
            path = path.split("/")[0] if "/" in path else path

            if path in ("product", "email"):
                if identifier is not None:
                    item = config.get(path, {}).get(identifier, None)
                    if item:
                        return {
                            "status": "success",
                            "data": item,
                            "action": "GET",
                            "path": f"{path}/{identifier}",
                        }
                    else:
                        return {
                            "status": "error",
                            "message": f"Item com '{path}_id' {identifier} não encontrado.",
                            "action": "GET",
                            "path": f"{path}/{identifier}",
                        }
                else:
                    items = config.get(path, {})
                    if pure:
                        return {
                            "status": "success",
                            "data": items,
                            "action": "GET",
                            "path": path,
                        }
                    items = [items[key] for key in items]
                    return {
                        "status": "success",
                        "data": items,
                        "action": "GET",
                        "path": path,
                    }
            match path:
                case "config":
                    return {
                        "status": "success",
                        "data": config,
                        "action": "GET",
                        "path": path,
                    }
                case "usb_devices":
                    return {
                        "status": "success",
                        "data": __class__.get_plugged_usb_devices(),
                        "action": "GET",
                        "path": path,
                    }
                case "serial_devices":
                    return {
                        "status": "success",
                        "data": __class__.get_plugged_serial_devices(),
                        "action": "GET",
                        "path": path,
                    }
                case "usb_printer":
                    return {
                        "status": "success",
                        "data": config.get("usb_printer", ""),
                        "action": "GET",
                        "path": path,
                    }
                case "usb_scaler":
                    return {
                        "status": "success",
                        "data": config.get("usb_scaler", ""),
                        "action": "GET",
                        "path": path,
                    }
                case "weighings":
                    return {
                        "status": "success",
                        "data": get_today_data_as_json(),
                        "action": "GET",
                        "path": path,
                    }
                case "config_printer_layouts":
                    return {
                        "status": "success",
                        "data": [
                            {
                                "name": "Layout Tabela",
                                "value": 1
                            },
                            {
                                "name": "Layout Textual",
                                "value": 2
                            }
                        ],
                        "action": "GET",
                        "path": path,
                    }
                case _:
                    return {
                        "status": "success",
                        "data": config.get(path, None),
                        "action": "GET",
                        "path": path,
                    }
        except Exception as e:  # pylint: disable=broad-except
            import traceback

            traceback.print_exc()
            logger.error("Erro no método GET: %s", e)
            return {
                "status": "error",
                "message": f"Erro no método GET: {e}",
                "action": "GET",
                "path": path,
            }

    def delete(self, path: str, identifier: Optional[int] = None) -> Dict[str, Any]:
        """
        Deleta dados do caminho especificado.

        Parameters
        ----------
        path : str
            Caminho do arquivo ('products', 'usb_printer', etc.).
        identifier : Optional[int]
            ID específico para deletar (por exemplo, 'product_id').

        Returns
        -------
        Dict[str, Any]
            Resultado da operação.
        """
        try:
            config = self._read_config()

            removed_data = None
            identifier = path.split("/")[-1] if "/" in path else None
            path = path.split("/")[0] if "/" in path else path

            if path in ("product", "email"):
                if not isinstance(config.get(path), dict):
                    logger.error("'%s' não está corretamente estruturado.", path)
                    return {
                        "status": "error",
                        "message": f"'{path}' não está corretamente estruturado.",
                        "action": "DELETE",
                        "path": path,
                    }

                if identifier is not None:
                    removed_data = config[path].pop(str(identifier), None)
                    if removed_data:
                        self._write_config(config)
                        return {
                            "status": "success",
                            "message": f"{path.capitalize()} {identifier} deletado com sucesso.",
                            "data": removed_data,
                            "action": "DELETE",
                            "path": f"{path}/{identifier}",
                        }
                    else:
                        logger.warning(
                            "item com '%s_id' %s não encontrado.", path, identifier
                        )
                        return {
                            "status": "error",
                            "message": f"Item com '{path}_id' {identifier} não encontrado.",
                            "action": "DELETE",
                            "path": f"{path}/{identifier}",
                        }
                else:
                    removed_data = config.pop(path, {})
                    logger.info("Todos os %ss foram deletados.", path)
            else:
                if path in config:
                    removed_data = config.pop(path)
                    logger.info("Path '%s' deletado.", path)
                else:
                    logger.warning("Path '%s' não encontrado.", path)
                    return {
                        "status": "error",
                        "message": f"Path '{path}' não encontrado.",
                        "action": "DELETE",
                        "path": path,
                    }

            self._write_config(config)
            return {
                "status": "success",
                "message": "Dados deletados com sucesso.",
                "data": removed_data,
                "action": "DELETE",
                "path": path,
            }
        except Exception as e:  # pylint: disable=broad-except
            logger.error("Erro no método DELETE: %s", e)
            return {
                "status": "error",
                "message": f"Erro no método DELETE: {e}",
                "action": "DELETE",
                "path": path,
            }

    def add_item(self, item_type: str, data: dict, config: dict) -> dict:
        """
        Método auxiliar para adicionar itens em listas ao config.json.

        Parameters
        ----------
        item_type : str
            Tipo do item ('product', 'email', etc.).
        data : dict
            Dados do item.
        config : dict
            Configuração atual.

        Returns
        -------
        dict
            Configuração atualizada.
        """
        if data == {}:
            logger.warning("Sem item para cadastrar.")
            return config

        validation_class: BaseModel = getattr(models, item_type.capitalize())

        if data.get(f"{item_type}_id", None) is not None:
            logger.info(
                "Item com '%s_id' %s já existe, atualizando...",
                item_type,
                data[f"{item_type}_id"],
            )
            logger.info(item_type.capitalize())
            logger.info(validation_class)
            item = validation_class(**data)
            config[f"{item_type}"][str(data[f"{item_type}_id"])] = item.model_dump()
            return config
        data[f"{item_type}_id"] = self.get_last_item_id(item_type, config) + 1
        item = validation_class(**data)
        config[f"{item_type}"][getattr(item, f"{item_type}_id")] = item.model_dump()
        logger.info("Adicionado um item.")
        return config

    @staticmethod
    def get_last_item_id(item_type: str, config: dict) -> int:
        """
        Retorna o último ID do item.

        Parameters
        ----------
        item_type : str
            Tipo do item ('product', 'email', etc.).
        config: dict
            Configuração atual.

        Returns
        -------
        int
            Último ID do item.
        """
        items = config.get(f"{item_type}", {})
        if items == {}:
            return 0
        keys = list(map(int, items.keys()))
        return max(keys)

    @staticmethod
    def get_plugged_serial_devices() -> List[Dict[str, str]]:
        """
        Método para retornar todos os dispositivos USB conectados.

        Returns
        -------
        List[Dict[str, str]]
            Lista de dispositivos USB conectados.
        """
        devices = []
        for port in list_ports.comports():
            if port.description != "n/a":
                devices.extend(
                    [{"device": port.device, "description": port.description}]
                )

        # Caso nenhum dispositivo seja encontrado
        if not devices:
            devices.append(
                {
                    "device": "Local - Teste",
                    "description": "Nenhum dispositivo USB conectado.",
                }
            )

        logger.info("Retornando %d dispositivos encontrados.", len(devices))
        return devices

    @staticmethod
    def get_plugged_usb_devices() -> List[Dict[str, str]]:
        """
        Método para retornar todos os dispositivos USB conectados.

        Returns
        -------
        List[Dict[str, str]]
            Lista de dispositivos USB conectados.
        """
        devices = []
        dev = usb.core.find(find_all=True)
        for device in dev:
            try:
                vendor_id = device.idVendor
                product_id = device.idProduct
                try:
                    usb_device = usb.util.get_string(device, device.iProduct)
                except Exception as e:  # pylint: disable=broad-except
                    logger.error("Erro ao buscar descrição do dispositivo: %s", e)
                    usb_device = "Desconhecido"
                devices.extend(
                    [{"device": f"{vendor_id}:{product_id}", "description": usb_device}]
                )
            except Exception as e:  # pylint: disable=broad-except
                logger.error("Erro ao buscar dispositivos USB: %s", e)

        # Caso nenhum dispositivo seja encontrado
        if not devices:
            devices.append(
                {
                    "device": "Local - Teste",
                    "description": "Nenhum dispositivo USB conectado.",
                }
            )

        logger.info("Retornando %d dispositivos encontrados.", len(devices))
        return devices
