import os
import logging
from datetime import datetime

import requests

from models import Request, Path, Action
from services.files.core import Files

logger = logging.getLogger(__name__)


class Api:
    """
    Service class for handling API requests to the Pesa e Pronto backend.

    Provides methods to process commands, send email batches, and synchronize requests.
    """

    def __init__(self) -> None:
        """
        Initializes the Api service with the base URL and file service.
        """
        # self.base_url = "https://api.pesaepronto.com.br"
        self.base_url = os.getenv("PESA_E_PRONTO_API", "https://api.pesaepronto.com.br")
        self.file_service = Files()
        self.creds = ("master", "PEYyeFu72Y")

    def sync_request(self, request: Request) -> None | dict:
        """
        Placeholder for synchronizing a request with the backend.

        Args:
            request (Request): The request object to synchronize.
        """
        try:
            match request.path:
                case "config_client":
                    self.save_user_data(request.data)
                case _:
                    return
        except requests.exceptions.RequestException as e:
            logger.error("Ocorreu um erro ao sincronizar os dados com a API!")
            logger.error(e)

    def save_user_data(self, data):
        url = f"{self.base_url}/cliente"
        user_data = self.file_service.get("config_client").get("data")
        if not user_data:
            logger.info("Dados do usuário inválidos ou corrompidos!")
            return
        body = {
            "situacao": 1,
            "nome": user_data.get("nome_cliente"),
            "nome_restaurante": user_data.get("nome_restaurante"),
            "frequencia_emails": user_data.get("frequencia_relatorio"),
            "endereco": user_data.get("endereco", ""),
            "data_faturamento": user_data.get(
                "data_faturamento", datetime.now().strftime("%Y-%m-%d")
            ),
            "data_inicio_contrato": user_data.get(
                "data_faturamento", datetime.now().strftime("%Y-%m-%d")
            ),
        }
        if user_data.get("id") is None:
            method = "post"
            url = url + "/save"
        else:
            method = "put"
            url = url + f"/{user_data.get('id')}"

        response = requests.request(method, url, json=body, auth=self.creds)

        logger.info(f"{response.status_code} - {response.text}")

        if response.status_code == 200 and method == "post":
            response_json: dict = response.json()
            user_id = response_json.get("id")
            user_data["id"] = user_id
            user_data["data_inicio_contrato"] = datetime.now().strftime("%Y-%m-%d")
            return self.file_service.post("config_client", user_data)

    def process_request(self, path: str) -> dict:
        """
        Processes an incoming API command based on the provided path.

        Args:
            path (str): The command path to process.

        Returns:
            dict: The result of the command execution.
        """
        match path:
            case Path.SEND_EMAIL:
                return self.send_email_batch()
            case _:
                return {
                    "status": "error",
                    "message": f"Comando '{path}' não suportado.",
                }

    def handle_email(self, request: Request) -> dict:
        """
        Handles emails requests, must have internet connection!

        Args:
            request (Request): The request
        """
        user_data = self.file_service.get("config_client")
        user_id = (user_data.get("data") or {}).get("id", None)
        response = {"status": "success", "action": request.action.name, "path": "email"}
        if user_id is None:
            return {"status": "error", "message": "Cliente não cadastrado ainda!", "action": request.action.value, "path": "email"}

        match request.action:
            case Action.POST:
                data = request.data
                if data is None:
                    return {"status": "error", "message": "Dados vazios!", "action": "POST", "path": "email"}
                
                body = {
                    "email": data.get("value", ""),
                    "cliente_id": user_id,
                    "ativo": data.get("active", False)
                }
                
                if email_id := request.data.get("email_id"):
                    url = f"{self.base_url}/email-cliente/email-cliente/{email_id}"
                    api_response = requests.put(url, json=body, auth=self.creds).json()
                    response["message"] = "Dados escritos com sucesso."
                    response["data"] = {
                        "email_id": api_response.get("id", email_id),
                        "value": api_response.get("email", body["email"]),
                        "active": api_response.get("ativo", body["ativo"])
                    }
                else:
                    url = f"{self.base_url}/email-cliente/cliente/email"
                    api_response = requests.post(url, json=body, auth=self.creds).json()
                    response["message"] = "Dados escritos com sucesso."
                    response["data"] = {
                        "email_id": api_response.get("id"),
                        "value": api_response.get("email", body["email"]),
                        "active": api_response.get("ativo", body["ativo"])
                    }
                return response

            case Action.GET:
                url = f"{self.base_url}/email-cliente/cliente/{user_id}"
                api_response = requests.get(url, auth=self.creds).json()
                if isinstance(api_response, list):
                    response["data"] = [
                        {
                            "email_id": item.get("id"),
                            "value": item.get("email"),
                            "active": item.get("ativo")
                        } for item in api_response
                    ]
                else:
                    response["data"] = []
                return response

            case Action.DELETE:
                if "/" in request.path:
                    email_id = request.path.split("/")[-1]
                    url = f"{self.base_url}/email-cliente/email-cliente/{email_id}"
                    api_response = requests.delete(url, auth=self.creds).json()
                    response["message"] = "Dados excluídos com sucesso."
                    return response
                else:
                    return {"status": "error", "message": "Deve ser fornecido um email específico!", "action": "DELETE", "path": "email"}

    def send_email_batch(self) -> None:
        """
        Sends a batch of emails using the backend API.

        Reads email addresses from the file service, constructs the request body,
        and sends a POST request to the backend. Logs the response and returns
        the status.

        Returns:
            dict: The result of the email sending operation.
        """
        user_data = self.file_service.get("config_client")
        user_id = user_data.get("data", {}).get("id", None)

        if not user_id:
            return {
                "status": "error",
                "message": "Usuário não cadastrado!",
            }

        url = f"{self.base_url}/email/enviar-emails-cliente/{user_id}"

        response = requests.get(url, auth=self.creds)

        logger.info(f"{response.status_code} - {response.json()}")

        if response.status_code == 200:
            return {
                "status": "success",
                "message": "Emails enviados!",
            }
        else:
            return {
                "status": "error",
                "message": f"Ocorreu um erro ao enviar os emails: {response.text}.",
            }

    def send_data(self):
        """
        Envia os dados de pesagem para a API.

        - Obtém o ID do usuário a partir do arquivo de configuração.
        - Busca os dados de pesagem associados ao usuário.
        - Se não houver dados ou se já foram enviados, registra a informação e retorna.
        - Se houver dados corrompidos ou ID de usuário ausente, retorna sem enviar.
        - Envia os dados para a API no endpoint '/pesagem/save-batch'.
        - Registra a resposta da API no log.
        """
        from services.report.record import get_data_for_api, clear_data_folder

        user_data = self.file_service.get("config_client")
        user_id = user_data.get("data", {}).get("id", None)

        data = get_data_for_api(user_id)

        if data == []:
            logger.info("Dados já enviados")
            return

        if data is None or user_id is None:
            # Enviar mensagem de dados corrompidos para a API
            return

        url = f"{self.base_url}/pesagem/save-batch"

        response = requests.post(url=url, json=data, auth=self.creds)

        logger.info(f"{response.status_code} - {response.text}")

        if response.status_code == 200:
            clear_data_folder()
