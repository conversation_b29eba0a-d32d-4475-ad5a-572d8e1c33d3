"""Módulo para validação da estrutura da comanda."""

from typing import Literal

from services.printers.real import print_file
from controllers.serial.serial_writer import write_port
from controllers.state.core import GlobalState

from .structure import CheckStructure


class Check:
    """
    Classe base para validação da estrutura da comanda.

    Parameters
    ----------
    structure : str
        Estrutura da comanda, um texto com espaços
        reservados para os argumentos.
    args : dict
        Argumentos a serem inseridos na estrutura.
    """

    def __init__(self, structure: CheckStructure):
        self.structure = structure

    def print_check(
        self, printer: str, barcode_mode: bool,
        barcode_type: str, product_code: str | None, product_codes: list,
        layout: Literal["1", "2"], state: GlobalState
    ) -> int | None:
        """
        Imprime a estrutura da comanda com os argumentos inseridos.
        """
        # Tentar imprimir na impressora, se não conseguir,
        # escrever na porta serial (emulando a impressora)
        try:
            if isinstance(printer, str):
                return print_file(
                    porta=printer,
                    dados=self.structure,
                    state=state,
                    barcode_mode=barcode_mode,
                    barcode_type=barcode_type,
                    product_code=product_code,
                    product_codes=product_codes,
                    layout=layout
                )
            else:
                raise Exception(f"Impressora não configurada: {printer}")
        except Exception as e:  # pylint: disable=broad-except
            print(f"Erro: {e}")
            return write_port(dados=self.structure)
        finally:
            pass
