import os
import sys

from PIL import Image, ImageDraw, ImageFont
from utils.handle_path import get_config_path


class CheckStructure:
    def __init__(
        self, company: str, check_time: str,
        weight: str, price: float, products: dict,
        weighting_id: int, blank_lines: int, family_mode_state: list = []
    ) -> None:
        self.company = company
        self.check_time = check_time
        self.weight = weight
        self.price = price
        self.products = products
        self.weighting_id = weighting_id
        self.blank_lines = blank_lines
        self.family_mode_state = family_mode_state

    def _prepare_products(self) -> str:
        string = ""
        for product in self.products.values():
            if product.get("active"):
                name: str = product.get("name", "")
                if len(name) > 14:
                    name = name[:14]
                name = name.ljust(14)
                price = f"R$ {product.get('price', 0):.2f}".ljust(10)
                string += f"{name} [ ][ ][ ][ ][ ][ ] {price}\n"

        for i in range(self.blank_lines):
            string += "_"*40 + "\n"

        return string

    def get_header(self) -> str:
        return f"""{self.company}
---------------------------------------
{self.check_time} - V&M Soluçoes
---------------------------------------
"""

    def get_weight_and_price(self) -> str:
        if not self.family_mode_state:
            return f"""
Comanda: #{self.weighting_id}
Peso: {self.weight}kg
Preço: R${self.price:.2f}\n
"""
        weight_and_price_text = f"\nComanda: #{self.weighting_id}\n"
        total_price = .0
        for state in self.family_mode_state:
            weight = state["peso"]
            price = float(state["preco"])
            total_price += price
            weight_and_price_text += f"""Peso: {weight}kg
Preço: R${price:.2f}\n
---------------------
"""
        weight_and_price_text += f"\nTotal: R${total_price:.2f}\n\n"
        return weight_and_price_text

    def get_products(self) -> str:
        products_text = self._prepare_products()
        if products_text != "":
            products_text = f"Produtos Adicionais:\n{products_text}"
        return products_text

    def get_products_image(self) -> Image:
        products_full = self.products.values()
        products = []
        for product in products_full:
            if product.get("active", False):
                products.append(product)

        col1_width = 290  # Largura para nomes (reduzida para compensar)
        col2_width = 120  # Largura para quantidade (dobrada: 60 -> 120)
        col3_width = 150  # Largura para preços (mantida)
        row_height = 70   # Altura de cada linha (reduzida)
        header_height = 80  # Altura do cabeçalho (reduzida)
        padding = 10      # Espaçamento interno (reduzido)
        total_width = col1_width + col2_width + \
            col3_width  # Total: 560px (cabe em 80mm)
        total_height = header_height + \
            ((len(products) + self.blank_lines) * row_height)

        # Criar imagem com Pillow
        image = Image.new(
            'L',
            (total_width, total_height),
            255
        )  # Fundo branco
        draw = ImageDraw.Draw(image)

        # Determine the base path for resources
        if getattr(sys, "frozen", False):
            # Executando via PyInstaller
            base_path = sys._MEIPASS
        else:
            # Executando via fonte
            base_path = os.path.dirname(os.path.abspath(__file__))
            base_path = os.path.abspath(os.path.join(base_path, "../.."))

        print(base_path)

        # Carregar fontes com tamanhos ajustados para papel menor
        try:
            font_path = os.path.join(base_path, "fonts", "Arial.ttf")
            font_header = ImageFont.truetype(
                font_path, 23
            )      # Cabeçalho menor
            font_normal = ImageFont.truetype(
                font_path, 26
            )      # Produto: 24 + 50% = 36px
            font_price = ImageFont.truetype(
                font_path, 30
            )       # Preço: 26 + 50% = 39px
            print("Fontes carregadas com sucesso.")
        except Exception as e:
            print("Erro ao carregar fonte:", e)
            font_header = ImageFont.load_default(size=18)
            font_normal = ImageFont.load_default(size=36)
            font_price = ImageFont.load_default(size=30)

        # Desenhar fundo do cabeçalho (cinza claro)
        draw.rectangle(
            [(0, 0), (total_width, header_height)],
            fill=220,
            outline=0,
            width=3
        )

        # Desenhar bordas principais
        draw.rectangle(
            [(0, 0), (total_width-1, total_height-1)],
            fill=None,
            outline=0,
            width=4
        )

        # Desenhar linhas verticais
        draw.line(
            [(col1_width, 0), (col1_width, total_height)],
            fill=0, width=3
        )
        draw.line(
            [
                (col1_width + col2_width, 0),
                (col1_width + col2_width, total_height)
            ],
            fill=0,
            width=3
        )

        # Desenhar linha horizontal separando cabeçalho
        draw.line(
            [(0, header_height), (total_width, header_height)],
            fill=0, width=4
        )

        # Desenhar linhas horizontais entre produtos
        for i in range(1, len(products) + self.blank_lines):
            y = header_height + (i * row_height)
            draw.line([(0, y), (total_width, y)], fill=0, width=2)

        # Adicionar texto do cabeçalho
        header_y = (header_height - 18) // 2  # Ajustado para fonte menor
        draw.text((padding, header_y), "PRODUTO", fill=0, font=font_header)

        # Centralizar "QTD" na coluna
        qtd_header_text = "QTD"
        qtd_header_width = draw.textlength(qtd_header_text, font=font_header)
        qtd_header_x = col1_width + ((col2_width - qtd_header_width) // 2)
        draw.text((qtd_header_x, header_y),
                  qtd_header_text, fill=0, font=font_header)

        # Calcular posição centralizada para "PREÇO"
        preco_text = "PREÇO"
        preco_width = draw.textlength(preco_text, font=font_header)
        preco_x = col1_width + col2_width + ((col3_width - preco_width) // 2)
        draw.text((preco_x, header_y), preco_text, fill=0, font=font_header)

        # Adicionar dados dos produtos com melhor formatação
        for i, product in enumerate(products):
            y_base = header_height + (i * row_height)
            # Ajustado para fonte menor
            text_y = y_base + ((row_height - 16) // 2)

            # Nome do produto (alinhado à esquerda, truncado se necessário)
            nome_max_width = col1_width - (2 * padding)
            nome_text = product.get("name")
            if draw.textlength(nome_text, font=font_normal) > nome_max_width:
                # Truncar o nome se for muito longo
                while (
                    draw.textlength(nome_text + "...", font=font_normal) > nome_max_width and len(nome_text) > 1
                ):
                    nome_text = nome_text[:-1]
                nome_text += "..."
            draw.text((padding, text_y), nome_text, fill=0, font=font_normal)

            price = f"R${product.get('price', 0):.2f}".replace(".", ",")

            preco_width = draw.textlength(price, font=font_price)
            preco_x = total_width - preco_width - padding
            draw.text(
                (preco_x, text_y),
                price,
                fill=0,
                font=font_price
            )

        return image

    def get_footer(self) -> str:
        return r"""
pesaepronto.com.br
"""

    def preview(self) -> None:
        full_check = ""
        for line in self.get_header().splitlines():
            full_check += line
        for line in self.get_weight_and_price().splitlines():
            full_check += line
        for line in self.get_products().splitlines():
            full_check += line
        for line in self.get_footer().splitlines():
            full_check += line
        print(full_check)
