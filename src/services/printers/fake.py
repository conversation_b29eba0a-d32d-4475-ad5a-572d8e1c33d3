import os
import argparse


def gerar_comanda(pasta, total, peso, preco_kg, itens):
    try:
        # Cria a pasta 'comandas' se não existir
        if not os.path.exists(pasta):
            os.makedirs(pasta)
            print(f"Pasta '{pasta}' criada.")

        # Lista os arquivos existentes na pasta e encontra o maior número
        arquivos = [
            f
            for f in os.listdir(pasta)
            if f.startswith("comanda_") and f.endswith(".txt")
        ]
        numeros_existentes = [int(f.split("_")[1].split(".")[0]) for f in arquivos]
        proximo_numero = max(numeros_existentes, default=0) + 1

        # Define o nome do próximo arquivo
        arquivo_novo = os.path.join(pasta, f"comanda_{proximo_numero}.txt")

        # Formata os dados da comanda
        comanda = f"""
OLIVA EMPORIO E BISTRO
Comanda: {proximo_numero}                 {os.popen("date").read().strip()}

-------------------------------------
VALOR                           R$ {total:.2f}

Tara: 0.056 Peso(KG): {peso:.3f} Preço/KG: {preco_kg:.2f}
-------------------------------------
Itens consumidos:
{"Produto".ljust(30)} {"Qtd".ljust(5)} {"Preço".ljust(7)} {"Total"}
{"-" * 50}
"""

        for item in itens:
            nome, qtd, preco_unit = item
            total_item = qtd * preco_unit
            comanda += f"{nome.ljust(30)} {str(qtd).ljust(5)} R$ {preco_unit:.2f}  R$ {total_item:.2f}\n"

        comanda += f"""
-------------------------------------
TOTAL A PAGAR R$ {total:.2f}
-------------------------------------

Obrigado pela preferência!
        """

        # Escreve os dados no arquivo
        with open(arquivo_novo, "w") as file:
            file.write(comanda)
        print(f"Dados gravados em '{arquivo_novo}'.")

    except Exception as e:
        print(f"Erro ao gerar comanda: {e}")


def gerar_comanda_2(comanda: str):
    try:
        # Cria a pasta 'comandas' se não existir
        pasta = "comandas"
        if not os.path.exists(pasta):
            os.makedirs(pasta)
            print(f"Pasta '{pasta}' criada.")

        # Lista os arquivos existentes na pasta e encontra o maior número
        arquivos = [
            f
            for f in os.listdir(pasta)
            if f.startswith("comanda_") and f.endswith(".txt")
        ]
        numeros_existentes = [int(f.split("_")[1].split(".")[0]) for f in arquivos]
        proximo_numero = max(numeros_existentes, default=0) + 1

        # Define o nome do próximo arquivo
        arquivo_novo = os.path.join(pasta, f"comanda_{proximo_numero}.txt")

        # Escreve os dados no arquivo
        with open(arquivo_novo, "w") as file:
            file.write(comanda)
        print(f"Dados gravados em '{arquivo_novo}'.")

    except Exception as e:
        print(f"Erro ao gerar comanda: {e}")


if __name__ == "__main__":
    # Configura o argparse para receber argumentos da linha de comando
    parser = argparse.ArgumentParser(description="Gerador de comandas")
    parser.add_argument("--peso", type=float, required=True, help="Peso em KG")
    parser.add_argument("--preco_kg", type=float, required=True, help="Preço por KG")
    parser.add_argument("--total", type=float, required=True, help="Total a pagar")
    parser.add_argument(
        "--itens",
        type=str,
        required=True,
        help="Itens consumidos no formato: 'nome,qtd,preco;nome,qtd,preco'",
    )
    parser.add_argument(
        "--pasta",
        type=str,
        default="comandas",
        help="Pasta para salvar as comandas (padrão: 'comandas')",
    )

    args = parser.parse_args()

    # Processa os itens consumidos
    itens = []
    for item_str in args.itens.split(";"):
        partes = item_str.split(",")
        nome = partes[0].strip()
        qtd = int(partes[1].strip())
        preco = float(partes[2].strip())
        itens.append((nome, qtd, preco))

    # Gera a comanda
    gerar_comanda(args.pasta, args.total, args.peso, args.preco_kg, itens)
