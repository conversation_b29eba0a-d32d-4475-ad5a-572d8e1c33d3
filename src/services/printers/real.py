"""<PERSON>ó<PERSON>lo para impressão de texto em impressoras térmicas."""

import logging
from typing import Literal

from escpos.printer import Usb

from controllers.state.core import GlobalState
from services.check.structure import CheckStructure

logger = logging.getLogger(__name__)


def print_products(
    printer: Usb, dados: CheckStructure, layout: Literal["1", "2"]
) -> None:
    match layout:
        case "1":
            products = dados.get_products_image()
            printer.image(products)
        case "2":
            products = dados.get_products()
            for line in products.splitlines():
                printer.text(line + "\n")


def print_barcode(
    printer: Usb, product_code: str, barcode_type: str
):
    logger.info(
        "product_code=%s", product_code
    )
    logger.info(
        "barcode_type=%s", barcode_type
    )

    if barcode_type.lower() == "code128":
        code = r"{A" + product_code

    if printer.check_barcode(
        code=code, bc=barcode_type
    ):
        printer.barcode(
            code=code, bc=barcode_type
        )
    else:
        logger.info("Código de barras inválido!")


def cut(printer: Usb):
    printer.cut()


def print_file(
    porta: str, dados: CheckStructure, state: GlobalState,
    barcode_mode: bool = False, barcode_type: str = "CODE128",
    product_code: str | None = None, product_codes: list = [],
    layout: Literal["1", "2"] = "1"
) -> int:
    """
    Imprime o texto na impressora.

    Parameters
    ----------
    porta : str
        Porta da impressora.
    dados : CheckStructure
        Texto a ser impresso.
    """
    try:
        # Configure a porta da impressora
        vendor_id, product_id = map(int, porta.split(":"))
        printer = Usb(vendor_id, product_id)
        printer.set(align="center")

        for (i, line) in enumerate(dados.get_header().splitlines()):
            if i == 0:
                printer.set(
                    align="center", bold=True, width=2,
                    height=1, custom_size=True
                )
            if i == 1:
                printer.set(align="center")
            printer.text(line + "\n")

        lines = dados.get_weight_and_price().splitlines()
        printer.set(align="center")
        for i, line in enumerate(lines):
            if i == 2:
                printer.set(
                    align="center", bold=True, width=2,
                    height=2, custom_size=True
                )
            printer.text(line + "\n")

        printer.set(align="center")

        print_products(printer, dados, layout)

        if barcode_mode and product_code:
            printer.text("\n\n")
            print_barcode(printer, product_code, barcode_type)

        elif barcode_mode and product_codes:
            printer.text("\n\n")
            for code in product_codes:
                print_barcode(printer, code, barcode_type)


        for line in dados.get_footer().splitlines():
            printer.text(line + "\n")

        cut(printer)

        logger.info("Impressão enviada com sucesso.")
        paper_status = printer.paper_status()
        printer.close()
        return paper_status
    except Exception as e:  # pylint: disable=broad-except
        logger.exception("Erro ao imprimir na impressora %s: %s", porta, e)
        printer.cut()
        printer.close()
