{"encodings": {"CP1001": {"name": "Unimplemented Star-specific CP1001"}, "CP1098": {"name": "CP1098"}, "CP1125": {"iconv": "CP1125", "name": "CP1125", "python_encode": "cp1125"}, "CP1250": {"iconv": "CP1250", "name": "CP1250", "python_encode": "cp1250"}, "CP1251": {"iconv": "CP1251", "name": "CP1251", "python_encode": "cp1251"}, "CP1252": {"iconv": "CP1252", "name": "CP1252", "python_encode": "cp1252"}, "CP1253": {"iconv": "CP1253", "name": "CP1253", "python_encode": "cp1253"}, "CP1254": {"iconv": "CP1254", "name": "CP1254", "python_encode": "cp1254"}, "CP1255": {"iconv": "CP1255", "name": "CP1255", "python_encode": "cp1255"}, "CP1256": {"iconv": "CP1256", "name": "CP1256", "python_encode": "cp1256"}, "CP1257": {"iconv": "CP1257", "name": "CP1257", "python_encode": "cp1257"}, "CP1258": {"iconv": "CP1258", "name": "CP1258", "python_encode": "cp1258"}, "CP2001": {"name": "Unimplemented Star-specific CP2001"}, "CP3001": {"name": "Unimplemented Star-specific CP3001"}, "CP3002": {"name": "Unimplemented Star-specific CP3002"}, "CP3011": {"data": ["ÇüéâäàåçêëèïîìÄÅ", "ÉæÆôöòûùÿÖÜ¢£¥₧ƒ", "áíóúñÑªº¿⌐¬½¼¡«»", "░▒▓│┤Ā╢ņ╕╣║╗╝╜╛┐", "└┴┬├─┼ā╟╚╔╩╦╠═╬╧", "Š╤čČ╘╒ģĪī┘┌█▄ūŪ▀", "αßΓπΣσµτΦΘΩδ∞φε∩", "ĒēĢķĶļĻžŽ∙·√Ņš■ "], "name": "CP3011 Latvian"}, "CP3012": {"data": ["АБВГДЕЖЗИЙКЛМНОП", "РСТУФХЦЧШЩЪЫЬЭЮЯ", "абвгдежзийклмноп", "░▒▓│┤Ā╢ņ╕╣║╗╝Ō╛┐", "└┴┬├─┼ā╟╚╔╩╦╠═╬╧", "Š╤čČ╘╒ģĪī┘┌█▄ūŪ▀", "рстуфхцчшщъыьэюя", "ĒēĢķĶļĻžŽ∙·√Ņš■ "], "name": "CP3012 Cyrillic"}, "CP3021": {"name": "Unimplemented Star-specific CP3021"}, "CP3041": {"name": "Unimplemented Star-specific CP3041"}, "CP3840": {"name": "Unimplemented Star-specific CP3840"}, "CP3841": {"name": "Unimplemented Star-specific CP3841"}, "CP3843": {"name": "Unimplemented Star-specific CP3843"}, "CP3844": {"name": "Unimplemented Star-specific CP3844"}, "CP3845": {"name": "Unimplemented Star-specific CP3845"}, "CP3846": {"name": "Unimplemented Star-specific CP3846"}, "CP3847": {"name": "Unimplemented Star-specific CP3847"}, "CP3848": {"name": "Unimplemented Star-specific CP3848"}, "CP437": {"iconv": "CP437", "name": "CP437", "python_encode": "cp437"}, "CP720": {"name": "CP720", "python_encode": "cp720"}, "CP737": {"iconv": "CP737", "name": "CP737", "python_encode": "cp737"}, "CP747": {"name": "CP747"}, "CP772": {"iconv": "CP772", "name": "CP772"}, "CP774": {"iconv": "CP774", "name": "CP774"}, "CP775": {"iconv": "CP775", "name": "CP775", "python_encode": "cp775"}, "CP850": {"iconv": "CP850", "name": "CP850", "python_encode": "cp850"}, "CP851": {"name": "Greek CP851", "notes": "Not used, due to inconsistencies between implementations."}, "CP852": {"iconv": "CP852", "name": "CP852", "python_encode": "cp852"}, "CP853": {"name": "CP853"}, "CP855": {"iconv": "CP855", "name": "CP855", "python_encode": "cp855"}, "CP856": {"iconv": "CP856", "name": "CP856", "python_encode": "cp856"}, "CP857": {"iconv": "CP857", "name": "CP857", "python_encode": "cp857"}, "CP858": {"name": "CP858", "python_encode": "cp858"}, "CP860": {"iconv": "CP860", "name": "CP860", "python_encode": "cp860"}, "CP861": {"iconv": "CP861", "name": "CP861", "python_encode": "cp861"}, "CP862": {"iconv": "CP862", "name": "CP862", "python_encode": "cp862"}, "CP863": {"iconv": "CP863", "name": "CP863", "python_encode": "cp863"}, "CP864": {"iconv": "CP864", "name": "CP864", "python_encode": "cp864"}, "CP865": {"iconv": "CP865", "name": "CP865", "python_encode": "cp865"}, "CP866": {"iconv": "CP866", "name": "CP866", "python_encode": "cp866"}, "CP869": {"iconv": "CP869", "name": "CP869", "python_encode": "cp869"}, "CP874": {"iconv": "CP874", "name": "CP874", "python_encode": "cp874"}, "CP928": {"name": "CP928"}, "CP932": {"iconv": "CP932", "name": "CP932", "python_encode": "cp932"}, "ISO_8859-1": {"iconv": "ISO_8859-1", "name": "ISO_8859-1", "python_encode": "latin_1"}, "ISO_8859-15": {"iconv": "ISO_8859-15", "name": "ISO_8859-15", "python_encode": "iso8859-15"}, "ISO_8859-2": {"iconv": "ISO_8859-2", "name": "ISO_8859-2", "python_encode": "iso8859_2"}, "ISO_8859-3": {"iconv": "ISO_8859-3", "name": "ISO_8859-3", "python_encode": "iso8859_3"}, "ISO_8859-4": {"iconv": "ISO_8859-4", "name": "ISO_8859-4", "python_encode": "iso8859_4"}, "ISO_8859-5": {"iconv": "ISO_8859-5", "name": "ISO_8859-5", "python_encode": "iso8859_5"}, "ISO_8859-6": {"iconv": "ISO_8859-6", "name": "ISO_8859-6", "python_encode": "iso8859_6"}, "ISO_8859-7": {"iconv": "ISO_8859-7", "name": "ISO_8859-7", "python_encode": "iso8859_7"}, "ISO_8859-8": {"iconv": "ISO_8859-8", "name": "ISO_8859-8", "python_encode": "iso8859_8"}, "ISO_8859-9": {"iconv": "ISO_8859-9", "name": "ISO_8859-9", "python_encode": "iso8859_9"}, "OXHOO-EUROPEAN": {"data": ["ÇüéâäàåçêëèïîìÄÅ", "ÉæÆôöòûùÿÖÜñÑªº¿", "áíóú¢£¥₧ƒ¡ÃãÕõØø", "·¨°`´½¼×÷≤≥«»≠√¯", "⌠⌡∞◤↵↑↓→←┌┐└┘•®©", "™†§¶Γ◢Θ         ", "ß   ε           ", "τ               "], "name": "Oxhoo-specific European"}, "RK1048": {"iconv": "RK1048", "name": "RK1048"}, "TCVN-3-1": {"data": ["                ", "                ", "        <PERSON><PERSON><PERSON><PERSON> ", "     àảãáạ ằẳẵắ ", "      ặầẩẫấậè ẻẽ", "éẹềểễếệìỉ   ĩíịò", " ỏõóọồổỗốộờởỡớợù", " ủũúụừửữứựỳỷỹýỵ "], "name": "Vietnamese TCVN-3 1"}, "TCVN-3-2": {"data": ["                ", "                ", " ĂÂ    Ð  ÊÔƠƯ  ", "     ÀẢÃÁẠ ẰẲẴẮ ", "      ẶẦẨẪẤẬÈ ẺẼ", "ÉẸỀỂỄẾỆÌỈ   ĨÍỊÒ", " ỎÕÓỌỒỔỖỐỘỜỞỠỚỢÙ", " ỦŨÚỤỪỬỮỨỰỲỶỸÝỴ "], "name": "Vietnamese TCVN-3 1"}, "Unknown": {"name": "Unknown", "notes": "Code page that has not yet been identified."}}, "profiles": {"AF-240": {"codePages": {"0": "OXHOO-EUROPEAN"}, "colors": {"0": "black"}, "features": {"barcodeA": false, "barcodeB": false, "bitImageColumn": false, "bitImageRaster": false, "graphics": false, "highDensity": false, "paperFullCut": false, "paperPartCut": false, "pdf417Code": false, "pulseBel": false, "pulseStandard": false, "qrCode": false, "starCommands": false}, "fonts": {"0": {"columns": 20, "name": "Font A"}}, "media": {"width": {"mm": 120, "pixels": 100}}, "name": "AF-240 Customer Display", "notes": "This is a two-line, ESC/POS-aware customer display from Oxhoo. The ESC/POS command mode can be activated persistently by sending:\n\n    echo -ne \"\\n\\x02\\x05\\x43\\x31\\x03\" > /dev/ttyUSB0\n", "vendor": "Oxhoo"}, "CT-S651": {"codePages": {"0": "CP437", "1": "CP932", "2": "CP850", "3": "CP860", "4": "CP863", "5": "CP865", "6": "CP852", "7": "CP866", "8": "CP857", "9": "CP1252", "16": "CP1252", "17": "CP866", "18": "CP852", "19": "CP858", "20": "Unknown", "21": "Unknown", "25": "Unknown", "26": "Unknown", "30": "TCVN-3-1", "31": "TCVN-3-2", "40": "CP864", "255": "Unknown"}, "colors": {"0": "black", "1": "red"}, "features": {"barcodeA": true, "barcodeB": true, "bitImageColumn": true, "bitImageRaster": true, "graphics": true, "highDensity": true, "paperFullCut": true, "paperPartCut": true, "pdf417Code": true, "pulseBel": true, "pulseStandard": true, "qrCode": true, "starCommands": false}, "fonts": {"0": {"columns": 48, "name": "Font A"}, "1": {"columns": 64, "name": "Font B"}, "2": {"columns": 72, "name": "Font C"}}, "media": {"width": {"mm": 80, "pixels": 640}}, "name": "CT-S651", "notes": "Citizen CT-S651 profile. This is a two-color thermal printer, supporting paper sizes from 58mm up to 83mm\n", "vendor": "Citizen"}, "NT-5890K": {"codePages": {"0": "CP437", "1": "CP932", "2": "CP850", "3": "CP860", "4": "CP863", "5": "CP865", "6": "Unknown", "7": "Unknown", "8": "Unknown", "9": "Unknown", "10": "Unknown", "16": "CP1252", "17": "CP866", "18": "CP852", "19": "CP858", "20": "Unknown", "21": "Unknown", "22": "Unknown", "23": "Unknown", "24": "CP747", "25": "CP1257", "27": "CP1258", "28": "CP864", "31": "Unknown", "32": "CP1255", "50": "CP437", "52": "CP437", "53": "CP858", "54": "CP852", "55": "CP860", "56": "CP861", "57": "CP863", "58": "CP865", "59": "CP866", "60": "CP855", "61": "CP857", "62": "CP862", "63": "CP864", "64": "CP737", "65": "CP851", "66": "CP869", "68": "CP772", "69": "CP774", "71": "CP1252", "72": "CP1250", "73": "CP1251", "74": "CP3840", "76": "CP3843", "77": "CP3844", "78": "CP3845", "79": "CP3846", "80": "CP3847", "81": "CP3848", "83": "CP2001", "84": "CP3001", "85": "CP3002", "86": "CP3011", "87": "CP3012", "88": "CP3021", "89": "CP3041", "90": "CP1253", "91": "CP1254", "92": "CP1256", "93": "CP720", "94": "CP1258", "95": "CP775", "96": "Unknown", "255": "Unknown"}, "colors": {"0": "black"}, "features": {"barcodeA": false, "barcodeB": false, "bitImageColumn": true, "bitImageRaster": true, "graphics": false, "highDensity": true, "paperFullCut": false, "paperPartCut": false, "pdf417Code": false, "pulseBel": false, "pulseStandard": true, "qrCode": false, "starCommands": false}, "fonts": {"0": {"columns": 32, "name": "Font A"}, "1": {"columns": 42, "name": "Font B"}}, "media": {"width": {"mm": 57.5, "pixels": 384}}, "name": "NT-5890K", "notes": "", "vendor": "<PERSON><PERSON>"}, "OCD-100": {"codePages": {"0": "CP437", "1": "CP932", "2": "CP850", "3": "CP860", "4": "CP863", "5": "CP865", "6": "Unknown", "7": "Unknown", "8": "Unknown", "9": "CP852", "10": "CP862", "11": "CP866", "12": "CP1251", "13": "CP1254", "14": "CP1255", "15": "CP1257", "16": "CP1252", "17": "CP1253", "19": "CP858"}, "colors": {"0": "black"}, "features": {"barcodeA": false, "barcodeB": false, "bitImageColumn": false, "bitImageRaster": false, "graphics": false, "highDensity": false, "paperFullCut": false, "paperPartCut": false, "pdf417Code": false, "pulseBel": false, "pulseStandard": false, "qrCode": false, "starCommands": false}, "fonts": {"0": {"columns": 20, "name": "Font A"}}, "media": {"width": {"mm": 180, "pixels": 100}}, "name": "OCD-100 Customer Display", "notes": "This is a two-line, ESC/POS-aware customer display from Aures. It has some graphics support via custom fonts, but is otherwise text-only. This profile is also suitable for the OCD-150 pole-mounted display.\n", "vendor": "<PERSON><PERSON>"}, "OCD-300": {"codePages": {"0": "CP437", "1": "CP932", "2": "CP850", "3": "CP860", "4": "CP863", "5": "CP865", "6": "Unknown", "7": "Unknown", "8": "Unknown", "9": "CP852", "10": "CP862", "11": "CP866", "12": "CP1251", "13": "CP1254", "14": "CP1255", "15": "CP1257", "16": "CP1252", "17": "CP1253", "18": "CP1250", "19": "CP858", "20": "Unknown"}, "colors": {"0": "black"}, "features": {"barcodeA": false, "barcodeB": false, "bitImageColumn": false, "bitImageRaster": false, "graphics": false, "highDensity": false, "paperFullCut": false, "paperPartCut": false, "pdf417Code": false, "pulseBel": false, "pulseStandard": false, "qrCode": false, "starCommands": false}, "fonts": {"0": {"columns": 20, "name": "Font A"}}, "media": {"width": {"mm": 130.2, "pixels": 240}}, "name": "OCD-300 Customer Display", "notes": "This is a two-line, ESC/POS-aware customer display from Aures. It has some graphics support via vendor-provided tools, but is otherwise text-only.\n", "vendor": "<PERSON><PERSON>"}, "P822D": {"codePages": {"0": "CP437", "1": "Unknown", "2": "CP850", "3": "CP860", "4": "CP863", "5": "CP865", "6": "Unknown", "7": "Unknown", "8": "Unknown", "9": "Unknown", "10": "Unknown", "16": "CP1252", "17": "CP866", "18": "CP852", "19": "CP858", "20": "Unknown", "21": "Unknown", "22": "Unknown", "23": "Unknown", "24": "CP747", "25": "CP1257", "27": "Unknown", "28": "CP864", "29": "CP1001", "30": "Unknown", "31": "Unknown", "32": "CP1255", "33": "CP720", "34": "CP1256", "35": "CP1257", "50": "CP437", "51": "Unknown", "52": "CP437", "53": "CP858", "54": "CP852", "55": "CP860", "56": "CP861", "57": "CP863", "58": "CP865", "59": "CP866", "60": "CP855", "61": "CP857", "62": "CP862", "63": "CP864", "64": "CP737", "65": "CP851", "66": "CP869", "67": "CP928", "68": "CP772", "69": "CP774", "70": "CP874", "71": "CP1252", "72": "CP1250", "73": "CP1251", "74": "CP3840", "75": "CP3841", "76": "CP3843", "77": "CP3844", "78": "CP3845", "79": "CP3846", "80": "CP3847", "81": "CP3848", "82": "CP1001", "83": "CP2001", "84": "CP3001", "85": "CP3002", "86": "CP3011", "87": "CP3012", "88": "CP3021", "89": "CP3041", "255": "Unknown"}, "colors": {"0": "black"}, "features": {"barcodeA": true, "barcodeB": true, "bitImageColumn": true, "bitImageRaster": true, "graphics": false, "highDensity": true, "paperFullCut": true, "paperPartCut": true, "pdf417Code": true, "pulseBel": false, "pulseStandard": true, "qrCode": true, "starCommands": false}, "fonts": {"0": {"columns": 42, "name": "Font A"}, "1": {"columns": 56, "name": "Font B"}}, "media": {"width": {"mm": "Unknown", "pixels": "Unknown"}}, "name": "P822D", "notes": "", "vendor": "PBM"}, "POS-5890": {"codePages": {"0": "CP437", "1": "CP932", "2": "CP850", "3": "CP860", "4": "CP863", "5": "CP865", "6": "Unknown", "7": "Unknown", "8": "Unknown", "9": "Unknown", "10": "Unknown", "16": "CP1252", "17": "CP866", "18": "CP852", "19": "CP858", "20": "Unknown", "21": "Unknown", "22": "Unknown", "23": "Unknown", "24": "CP747", "25": "CP1257", "27": "CP1258", "28": "CP864", "31": "Unknown", "32": "CP1255", "50": "CP437", "52": "CP437", "53": "CP858", "54": "CP852", "55": "CP860", "56": "CP861", "57": "CP863", "58": "CP865", "59": "CP866", "60": "CP855", "61": "CP857", "62": "CP862", "63": "CP864", "64": "CP737", "65": "CP851", "66": "CP869", "68": "CP772", "69": "CP774", "71": "CP1252", "72": "CP1250", "73": "CP1251", "74": "CP3840", "76": "CP3843", "77": "CP3844", "78": "CP3845", "79": "CP3846", "80": "CP3847", "81": "CP3848", "83": "CP2001", "84": "CP3001", "85": "CP3002", "86": "CP3011", "87": "CP3012", "88": "CP3021", "89": "CP3041", "90": "CP1253", "91": "CP1254", "92": "CP1256", "93": "CP720", "94": "CP1258", "95": "CP775", "96": "Unknown", "255": "Unknown"}, "colors": {"0": "black"}, "features": {"barcodeA": false, "barcodeB": false, "bitImageColumn": false, "bitImageRaster": true, "graphics": false, "highDensity": true, "paperFullCut": false, "paperPartCut": false, "pdf417Code": false, "pulseBel": false, "pulseStandard": true, "qrCode": false, "starCommands": false}, "fonts": {"0": {"columns": 32, "name": "Font A"}, "1": {"columns": 42, "name": "Font B"}}, "media": {"width": {"mm": 57.5, "pixels": 384}}, "name": "POS5890 Series", "notes": "POS-5890 thermal printer series, also marketed under various other names.\n", "vendor": "Zjiang"}, "RP326": {"codePages": {"0": "CP437", "1": "Unknown", "2": "CP850", "3": "CP860", "4": "CP863", "5": "CP865", "6": "CP1251", "7": "CP866", "8": "Unknown", "9": "Unknown", "10": "Unknown", "15": "CP862", "16": "CP1252", "17": "CP1253", "18": "CP852", "19": "CP858", "20": "Unknown", "21": "Unknown", "22": "Unknown", "23": "ISO_8859-1", "24": "CP737", "25": "CP1257", "26": "Unknown", "27": "CP720", "28": "CP855", "29": "CP857", "30": "CP1250", "31": "CP775", "32": "CP1254", "33": "CP1255", "34": "CP1256", "35": "CP1258", "36": "ISO_8859-2", "37": "ISO_8859-3", "38": "ISO_8859-4", "39": "ISO_8859-5", "40": "ISO_8859-6", "41": "ISO_8859-7", "42": "ISO_8859-8", "43": "ISO_8859-9", "44": "ISO_8859-15", "45": "Unknown", "46": "CP856", "47": "CP874"}, "colors": {"0": "black"}, "features": {"barcodeA": true, "barcodeB": true, "bitImageColumn": true, "bitImageRaster": true, "graphics": false, "highDensity": true, "paperFullCut": true, "paperPartCut": true, "pdf417Code": true, "pulseBel": false, "pulseStandard": true, "qrCode": true, "starCommands": false}, "fonts": {"0": {"columns": 42, "name": "Font A"}, "1": {"columns": 56, "name": "Font B"}}, "media": {"width": {"mm": "Unknown", "pixels": "Unknown"}}, "name": "RP326", "notes": "", "vendor": "<PERSON><PERSON><PERSON>"}, "SP2000": {"codePages": {"0": "CP437", "1": "CP437", "2": "CP932", "3": "CP437", "4": "CP858", "5": "CP852", "6": "CP860", "7": "CP861", "8": "CP863", "9": "CP865", "10": "CP866", "11": "CP855", "12": "CP857", "13": "CP862", "14": "CP864", "15": "CP737", "16": "CP851", "17": "CP869", "18": "CP928", "19": "CP772", "20": "CP774", "21": "CP874", "32": "CP1252", "33": "CP1250", "34": "CP1251", "64": "CP3840", "65": "CP3841", "66": "CP3843", "67": "CP3844", "68": "CP3845", "69": "CP3846", "70": "CP3847", "71": "CP3848", "72": "CP1001", "73": "CP2001", "74": "CP3001", "75": "CP3002", "76": "CP3011", "77": "CP3012", "78": "CP3021", "79": "CP3041", "96": "Unknown", "97": "Unknown", "98": "Unknown", "99": "Unknown", "100": "Unknown", "101": "Unknown", "102": "Unknown", "255": "Unknown"}, "colors": {"0": "black"}, "features": {"barcodeA": true, "barcodeB": true, "bitImageColumn": true, "bitImageRaster": true, "graphics": true, "highDensity": true, "paperFullCut": true, "paperPartCut": true, "pdf417Code": true, "pulseBel": false, "pulseStandard": true, "qrCode": true, "starCommands": true}, "fonts": {"0": {"columns": 42, "name": "Font A"}, "1": {"columns": 56, "name": "Font B"}}, "media": {"width": {"mm": "Unknown", "pixels": "Unknown"}}, "name": "SP2000 Series", "notes": "Star SP2000 impact printer series with ESC/POS emulation enabled", "vendor": "Star Micronics"}, "Sunmi-V2": {"codePages": {"0": "CP437", "2": "CP850", "3": "CP860", "4": "CP863", "5": "CP865", "13": "CP857", "14": "CP737", "15": "ISO_8859-7", "16": "CP1252", "17": "CP866", "18": "CP852", "19": "CP858", "21": "CP874", "33": "CP775", "34": "CP855", "36": "CP862", "37": "CP864", "254": "CP855"}, "colors": {"0": "black"}, "features": {"barcodeA": true, "barcodeB": true, "bitImageColumn": false, "bitImageRaster": true, "graphics": false, "highDensity": true, "paperFullCut": false, "paperPartCut": false, "pdf417Code": true, "pulseBel": false, "pulseStandard": true, "qrCode": true, "starCommands": false}, "fonts": {"0": {"columns": 32, "name": "Font A"}, "1": {"columns": 42, "name": "Font B"}}, "media": {"width": {"mm": 57.5, "pixels": 384}}, "name": "Sunmi V2", "notes": "Sunmi mini-POS Android device with a built-in Virtual Bluetooth thermal printer.\n", "vendor": "<PERSON><PERSON>"}, "TEP-200M": {"codePages": {"0": "CP437", "1": "CP932", "2": "CP850", "3": "CP860", "4": "CP863", "5": "CP865", "6": "Unknown", "7": "Unknown", "8": "Unknown", "11": "CP851", "12": "CP853", "13": "CP857", "14": "CP737", "15": "ISO_8859-7", "16": "CP1252", "17": "CP866", "18": "CP852", "19": "CP858", "20": "Unknown", "21": "CP874", "22": "Unknown", "23": "Unknown", "24": "Unknown", "25": "Unknown", "26": "Unknown", "30": "TCVN-3-1", "31": "TCVN-3-2", "32": "CP720", "33": "CP775", "34": "CP855", "35": "CP861", "36": "CP862", "37": "CP864", "38": "CP869", "39": "ISO_8859-2", "40": "ISO_8859-15", "41": "CP1098", "42": "CP774", "43": "CP772", "44": "CP1125", "45": "CP1250", "46": "CP1251", "47": "CP1253", "48": "CP1254", "49": "CP1255", "50": "CP1256", "51": "CP1257", "52": "CP1258", "53": "RK1048", "66": "Unknown", "67": "Unknown", "68": "Unknown", "69": "Unknown", "70": "Unknown", "71": "Unknown", "72": "Unknown", "73": "Unknown", "74": "Unknown", "75": "Unknown", "82": "Unknown", "254": "Unknown", "255": "Unknown"}, "colors": {"0": "black"}, "features": {"barcodeA": true, "barcodeB": true, "bitImageColumn": true, "bitImageRaster": true, "graphics": true, "highDensity": true, "paperFullCut": true, "paperPartCut": true, "pdf417Code": true, "pulseBel": false, "pulseStandard": true, "qrCode": true, "starCommands": false}, "fonts": {"0": {"columns": 42, "name": "Font A"}, "1": {"columns": 56, "name": "Font B"}}, "media": {"width": {"mm": "Unknown", "pixels": "Unknown"}}, "name": "TEP200M Series", "notes": "", "vendor": "EPOS"}, "TM-P80": {"codePages": {"0": "CP437", "1": "CP932", "2": "CP850", "3": "CP860", "4": "CP863", "5": "CP865", "6": "Unknown", "7": "Unknown", "8": "Unknown", "11": "CP851", "12": "CP853", "13": "CP857", "14": "CP737", "15": "ISO_8859-7", "16": "CP1252", "17": "CP866", "18": "CP852", "19": "CP858", "20": "Unknown", "21": "CP874", "22": "Unknown", "23": "Unknown", "24": "Unknown", "25": "Unknown", "26": "Unknown", "30": "TCVN-3-1", "31": "TCVN-3-2", "32": "CP720", "33": "CP775", "34": "CP855", "35": "CP861", "36": "CP862", "37": "CP864", "38": "CP869", "39": "ISO_8859-2", "40": "ISO_8859-15", "41": "CP1098", "42": "CP774", "43": "CP772", "44": "CP1125", "45": "CP1250", "46": "CP1251", "47": "CP1253", "48": "CP1254", "49": "CP1255", "50": "CP1256", "51": "CP1257", "52": "CP1258", "53": "RK1048", "66": "Unknown", "67": "Unknown", "68": "Unknown", "69": "Unknown", "70": "Unknown", "71": "Unknown", "72": "Unknown", "73": "Unknown", "74": "Unknown", "75": "Unknown", "82": "Unknown", "254": "Unknown", "255": "Unknown"}, "colors": {"0": "black"}, "features": {"barcodeA": true, "barcodeB": true, "bitImageColumn": true, "bitImageRaster": true, "graphics": true, "highDensity": true, "paperFullCut": true, "paperPartCut": true, "pdf417Code": true, "pulseBel": false, "pulseStandard": true, "qrCode": true, "starCommands": false}, "fonts": {"0": {"columns": 42, "name": "Font A"}, "1": {"columns": 56, "name": "Font B"}, "2": {"columns": 24, "name": "Kanji"}}, "media": {"width": {"mm": 72, "pixels": 576}}, "name": "TM-P80", "notes": "Portable printer (48-column mode)", "vendor": "<PERSON><PERSON><PERSON>"}, "TM-P80-42col": {"codePages": {"0": "CP437", "1": "CP932", "2": "CP850", "3": "CP860", "4": "CP863", "5": "CP865", "6": "Unknown", "7": "Unknown", "8": "Unknown", "11": "CP851", "12": "CP853", "13": "CP857", "14": "CP737", "15": "ISO_8859-7", "16": "CP1252", "17": "CP866", "18": "CP852", "19": "CP858", "20": "Unknown", "21": "CP874", "22": "Unknown", "23": "Unknown", "24": "Unknown", "25": "Unknown", "26": "Unknown", "30": "TCVN-3-1", "31": "TCVN-3-2", "32": "CP720", "33": "CP775", "34": "CP855", "35": "CP861", "36": "CP862", "37": "CP864", "38": "CP869", "39": "ISO_8859-2", "40": "ISO_8859-15", "41": "CP1098", "42": "CP774", "43": "CP772", "44": "CP1125", "45": "CP1250", "46": "CP1251", "47": "CP1253", "48": "CP1254", "49": "CP1255", "50": "CP1256", "51": "CP1257", "52": "CP1258", "53": "RK1048", "66": "Unknown", "67": "Unknown", "68": "Unknown", "69": "Unknown", "70": "Unknown", "71": "Unknown", "72": "Unknown", "73": "Unknown", "74": "Unknown", "75": "Unknown", "82": "Unknown", "254": "Unknown", "255": "Unknown"}, "colors": {"0": "black"}, "features": {"barcodeA": true, "barcodeB": true, "bitImageColumn": true, "bitImageRaster": true, "graphics": true, "highDensity": true, "paperFullCut": true, "paperPartCut": true, "pdf417Code": true, "pulseBel": false, "pulseStandard": true, "qrCode": true, "starCommands": false}, "fonts": {"0": {"columns": 42, "name": "Font A"}, "1": {"columns": 60, "name": "Font B"}, "2": {"columns": 21, "name": "Kanji"}}, "media": {"width": {"mm": 63.6, "pixels": 546}}, "name": "TM-P80 (42 column mode)", "notes": "Portable printer (42-column mode)", "vendor": "<PERSON><PERSON><PERSON>"}, "TM-T88II": {"codePages": {"0": "CP437", "1": "CP932", "2": "CP850", "3": "CP860", "4": "CP863", "5": "CP865", "19": "CP858", "255": "Unknown"}, "colors": {"0": "black"}, "features": {"barcodeA": true, "barcodeB": true, "bitImageColumn": true, "bitImageRaster": true, "graphics": true, "highDensity": true, "paperFullCut": true, "paperPartCut": true, "pdf417Code": true, "pulseBel": false, "pulseStandard": true, "qrCode": true, "starCommands": false}, "fonts": {"0": {"columns": 42, "name": "Font A"}, "1": {"columns": 56, "name": "Font B"}}, "media": {"width": {"mm": "Unknown", "pixels": "Unknown"}}, "name": "TM-T88II", "notes": "Epson TM-T88II profile. This printer is discontinued by the Vendor, and has similar feature support to the TM-T88III. The code page mapping is documented in the \"TM-T88II/T88III Technical Reference Guide\".\n", "vendor": "<PERSON><PERSON><PERSON>"}, "TM-T88III": {"codePages": {"0": "CP437", "1": "CP932", "2": "CP850", "3": "CP860", "4": "CP863", "5": "CP865", "16": "CP1252", "17": "CP866", "18": "CP862", "19": "CP858", "255": "Unknown"}, "colors": {"0": "black"}, "features": {"barcodeA": true, "barcodeB": true, "bitImageColumn": true, "bitImageRaster": true, "graphics": true, "highDensity": true, "paperFullCut": true, "paperPartCut": true, "pdf417Code": true, "pulseBel": false, "pulseStandard": true, "qrCode": true, "starCommands": false}, "fonts": {"0": {"columns": 42, "name": "Font A"}, "1": {"columns": 56, "name": "Font B"}}, "media": {"width": {"mm": "Unknown", "pixels": "Unknown"}}, "name": "TM-T88III", "notes": "Epson TM-T88III profile. This printer has similar feature support to the TM-T88II. The code page mapping is documented in the \"TM-T88II/T88III Technical Reference Guide\".\n", "vendor": "<PERSON><PERSON><PERSON>"}, "TM-T88IV": {"codePages": {"0": "CP437", "1": "CP932", "2": "CP850", "3": "CP860", "4": "CP863", "5": "CP865", "16": "CP1252", "17": "CP866", "18": "CP852", "19": "CP858", "255": "Unknown"}, "colors": {"0": "black"}, "features": {"barcodeA": true, "barcodeB": true, "bitImageColumn": true, "bitImageRaster": true, "graphics": true, "highDensity": true, "paperFullCut": true, "paperPartCut": true, "pdf417Code": true, "pulseBel": false, "pulseStandard": true, "qrCode": true, "starCommands": false}, "fonts": {"0": {"columns": 42, "name": "Font A"}, "1": {"columns": 56, "name": "Font B"}}, "media": {"width": {"mm": 80, "pixels": 512}}, "name": "TM-T88IV", "notes": "Epson TM-T88IV profile\n", "vendor": "<PERSON><PERSON><PERSON>"}, "TM-T88IV-SA": {"codePages": {"0": "CP437", "20": "Unknown", "21": "CP874", "26": "Unknown", "30": "TCVN-3-1", "31": "TCVN-3-2"}, "colors": {"0": "black"}, "features": {"barcodeA": true, "barcodeB": true, "bitImageColumn": true, "bitImageRaster": true, "graphics": true, "highDensity": true, "paperFullCut": true, "paperPartCut": true, "pdf417Code": true, "pulseBel": false, "pulseStandard": true, "qrCode": true, "starCommands": false}, "fonts": {"0": {"columns": 42, "name": "Font A"}, "1": {"columns": 56, "name": "Font B"}}, "media": {"width": {"mm": 80, "pixels": 512}}, "name": "TM-T88IV South Asia", "notes": "Epson TM-T88IV profile (South Asia models)\n", "vendor": "<PERSON><PERSON><PERSON>"}, "TM-T88V": {"codePages": {"0": "CP437", "1": "CP932", "2": "CP850", "3": "CP860", "4": "CP863", "5": "CP865", "11": "CP851", "12": "CP853", "13": "CP857", "14": "CP737", "15": "ISO_8859-7", "16": "CP1252", "17": "CP866", "18": "CP852", "19": "CP858", "30": "TCVN-3-1", "31": "TCVN-3-2", "32": "CP720", "33": "CP775", "34": "CP855", "35": "CP861", "36": "CP862", "37": "CP864", "38": "CP869", "39": "ISO_8859-2", "40": "ISO_8859-15", "41": "CP1098", "45": "CP1250", "46": "CP1251", "47": "CP1253", "48": "CP1254", "49": "CP1255", "50": "CP1256", "51": "CP1257", "52": "CP1258", "53": "RK1048", "255": "Unknown"}, "colors": {"0": "black"}, "features": {"barcodeA": true, "barcodeB": true, "bitImageColumn": true, "bitImageRaster": true, "graphics": true, "highDensity": true, "paperFullCut": true, "paperPartCut": true, "pdf417Code": true, "pulseBel": false, "pulseStandard": true, "qrCode": true, "starCommands": false}, "fonts": {"0": {"columns": 42, "name": "Font A"}, "1": {"columns": 56, "name": "Font B"}}, "media": {"width": {"mm": 80, "pixels": 512}}, "name": "TM-T88V", "notes": "Epson TM-T88V profile\n", "vendor": "<PERSON><PERSON><PERSON>"}, "TM-U220": {"codePages": {"0": "CP437"}, "colors": {"0": "black", "1": "alternate"}, "features": {"barcodeA": false, "barcodeB": false, "bitImageColumn": true, "bitImageRaster": false, "graphics": false, "highDensity": false, "paperFullCut": false, "paperPartCut": false, "pdf417Code": false, "pulseBel": false, "pulseStandard": true, "qrCode": false, "starCommands": false}, "fonts": {"0": {"columns": 42, "name": "Font A"}, "1": {"columns": 56, "name": "Font B"}}, "media": {"width": {"mm": 80, "pixels": "Unknown"}}, "name": "TM-U220", "notes": "Two-color impact printer with 80mm output", "vendor": "<PERSON><PERSON><PERSON>"}, "TSP600": {"codePages": {"0": "CP437", "1": "CP437", "2": "CP932", "3": "CP437", "4": "CP858", "5": "CP852", "6": "CP860", "7": "CP861", "8": "CP863", "9": "CP865", "10": "CP866", "11": "CP855", "12": "CP857", "13": "CP862", "14": "CP864", "15": "CP737", "16": "CP851", "17": "CP869", "18": "CP928", "19": "CP772", "20": "CP774", "21": "CP874", "32": "CP1252", "33": "CP1250", "34": "CP1251", "64": "CP3840", "65": "CP3841", "66": "CP3843", "67": "CP3844", "68": "CP3845", "69": "CP3846", "70": "CP3847", "71": "CP3848", "72": "CP1001", "73": "CP2001", "74": "CP3001", "75": "CP3002", "76": "CP3011", "77": "CP3012", "78": "CP3021", "79": "CP3041", "96": "Unknown", "97": "Unknown", "98": "Unknown", "99": "Unknown", "100": "Unknown", "101": "Unknown", "102": "Unknown", "255": "Unknown"}, "colors": {"0": "black"}, "features": {"barcodeA": true, "barcodeB": true, "bitImageColumn": true, "bitImageRaster": true, "graphics": true, "highDensity": true, "paperFullCut": true, "paperPartCut": true, "pdf417Code": true, "pulseBel": false, "pulseStandard": true, "qrCode": true, "starCommands": true}, "fonts": {"0": {"columns": 42, "name": "Font A"}, "1": {"columns": 56, "name": "Font B"}}, "media": {"width": {"mm": "Unknown", "pixels": "Unknown"}}, "name": "TSP600 Series", "notes": "Star TSP600 thermal printer series with ESC/POS emulation enabled", "vendor": "Star Micronics"}, "TUP500": {"codePages": {"0": "CP437", "1": "CP437", "2": "CP932", "3": "CP437", "4": "CP858", "5": "CP852", "6": "CP860", "7": "CP861", "8": "CP863", "9": "CP865", "10": "CP866", "11": "CP855", "12": "CP857", "13": "CP862", "14": "CP864", "15": "CP737", "16": "CP851", "17": "CP869", "18": "CP928", "19": "CP772", "20": "CP774", "21": "CP874", "32": "CP1252", "33": "CP1250", "34": "CP1251", "64": "CP3840", "65": "CP3841", "66": "CP3843", "67": "CP3844", "68": "CP3845", "69": "CP3846", "70": "CP3847", "71": "CP3848", "72": "CP1001", "73": "CP2001", "74": "CP3001", "75": "CP3002", "76": "CP3011", "77": "CP3012", "78": "CP3021", "79": "CP3041", "96": "Unknown", "97": "Unknown", "98": "Unknown", "99": "Unknown", "100": "Unknown", "101": "Unknown", "102": "Unknown", "255": "Unknown"}, "colors": {"0": "black"}, "features": {"barcodeA": true, "barcodeB": true, "bitImageColumn": true, "bitImageRaster": true, "graphics": true, "highDensity": true, "paperFullCut": true, "paperPartCut": true, "pdf417Code": true, "pulseBel": false, "pulseStandard": true, "qrCode": true, "starCommands": true}, "fonts": {"0": {"columns": 42, "name": "Font A"}, "1": {"columns": 56, "name": "Font B"}}, "media": {"width": {"mm": "Unknown", "pixels": "Unknown"}}, "name": "TUP500 Series", "notes": "Star TUP500 thermal printer series with ESC/POS emulation enabled", "vendor": "Star Micronics"}, "ZJ-5870": {"codePages": {"0": "CP437", "1": "CP932", "2": "CP850", "3": "CP860", "4": "CP863", "5": "CP865", "16": "CP1252", "17": "CP866", "18": "CP852"}, "colors": {"0": "black"}, "features": {"barcodeA": false, "barcodeB": false, "bitImageColumn": true, "bitImageRaster": true, "graphics": false, "highDensity": false, "paperFullCut": false, "paperPartCut": false, "pdf417Code": false, "pulseBel": false, "pulseStandard": true, "qrCode": false, "starCommands": false}, "fonts": {"0": {"columns": 32, "name": "Font A"}}, "media": {"width": {"mm": 48, "pixels": 384}}, "name": "ZJ-5870 Thermal Receipt Printer", "notes": "ESC/POS Profile for ZiJiang ZJ-5870 Thermal Receipt Printer, which may be branded AGPtEK or Esky, and identifies itself as a POS-58 Thermal Printer on selftest. This profile is suitable for alphanumberic character mode, but is untested on Chinese character mode. (Change modes by holding down feed button during power-on until the mode LED turns off, then release immediately.)\n", "vendor": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "default": {"codePages": {"0": "CP437", "1": "CP932", "2": "CP850", "3": "CP860", "4": "CP863", "5": "CP865", "6": "Unknown", "7": "Unknown", "8": "Unknown", "11": "CP851", "12": "CP853", "13": "CP857", "14": "CP737", "15": "ISO_8859-7", "16": "CP1252", "17": "CP866", "18": "CP852", "19": "CP858", "20": "Unknown", "21": "CP874", "22": "Unknown", "23": "Unknown", "24": "Unknown", "25": "Unknown", "26": "Unknown", "30": "TCVN-3-1", "31": "TCVN-3-2", "32": "CP720", "33": "CP775", "34": "CP855", "35": "CP861", "36": "CP862", "37": "CP864", "38": "CP869", "39": "ISO_8859-2", "40": "ISO_8859-15", "41": "CP1098", "42": "CP774", "43": "CP772", "44": "CP1125", "45": "CP1250", "46": "CP1251", "47": "CP1253", "48": "CP1254", "49": "CP1255", "50": "CP1256", "51": "CP1257", "52": "CP1258", "53": "RK1048", "66": "Unknown", "67": "Unknown", "68": "Unknown", "69": "Unknown", "70": "Unknown", "71": "Unknown", "72": "Unknown", "73": "Unknown", "74": "Unknown", "75": "Unknown", "82": "Unknown", "254": "Unknown", "255": "Unknown"}, "colors": {"0": "black"}, "features": {"barcodeA": true, "barcodeB": true, "bitImageColumn": true, "bitImageRaster": true, "graphics": true, "highDensity": true, "paperFullCut": true, "paperPartCut": true, "pdf417Code": true, "pulseBel": false, "pulseStandard": true, "qrCode": true, "starCommands": false}, "fonts": {"0": {"columns": 42, "name": "Font A"}, "1": {"columns": 56, "name": "Font B"}}, "media": {"width": {"mm": "Unknown", "pixels": "Unknown"}}, "name": "<PERSON><PERSON><PERSON>", "notes": "Default ESC/POS profile, suitable for standards-compliant or Epson-branded printers. This profile allows the use of standard ESC/POS features, and can encode a variety of code pages.\n", "vendor": "Generic"}, "simple": {"codePages": {"0": "CP437"}, "colors": {"0": "black"}, "features": {"barcodeA": false, "barcodeB": false, "bitImageColumn": false, "bitImageRaster": true, "graphics": false, "highDensity": true, "paperFullCut": false, "paperPartCut": false, "pdf417Code": false, "pulseBel": false, "pulseStandard": true, "qrCode": false, "starCommands": false}, "fonts": {"0": {"columns": 42, "name": "Font A"}, "1": {"columns": 56, "name": "Font B"}}, "media": {"width": {"mm": "Unknown", "pixels": "Unknown"}}, "name": "Simple", "notes": "A profile for use in printers with unknown or poor compatibility. This profile indicates that a small number of features are supported, so that commands are not sent a printer that is unlikely to understand them.\n", "vendor": "Generic"}}}