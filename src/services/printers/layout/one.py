from escpos.printer import Usb
import os
import sys
from PIL import Image, ImageDraw, ImageFont

# Configurar a impressora (substitua os IDs pelos da sua impressora)
printer = Usb(0x1fc9, 0x2016, 0)  # Exemplo: VendorID e ProductID

# Dados dos produtos
produtos = [
    {"nome": "Produto A", "preco": "R$ 10,00"},
    {"nome": "Produto B", "preco": "R$ 15,50"},
    {"nome": "Produto C", "preco": "R$ 22,75"},
    {"nome": "Produto D", "preco": "R$ 8,90"},
    {"nome": "Produto E", "preco": "R$ 33,25"},
    {"nome": "Produto F", "preco": "R$ 12,80"},
    {"nome": "Produto G", "preco": "R$ 45,00"},
]

# Configurações da tabela aprimoradas para papel 80mm
col1_width = 290  # Largura para nomes (reduzida para compensar)
col2_width = 120  # Largura para quantidade (dobrada: 60 -> 120)
col3_width = 150  # Largura para preços (mantida)
row_height = 70   # Altura de cada linha (reduzida)
header_height = 80  # Altura do cabeçalho (reduzida)
padding = 10      # Espaçamento interno (reduzido)
total_width = col1_width + col2_width + col3_width  # Total: 560px (cabe em 80mm)
total_height = header_height + (len(produtos) * row_height)  # Removido +20 para total

# Criar imagem com Pillow
image = Image.new('L', (total_width, total_height), 255)  # Fundo branco
draw = ImageDraw.Draw(image)

# Determine the base path for resources (for PyInstaller or development)
if getattr(sys, 'frozen', False) and hasattr(sys, '_MEIPASS'):
    # Running in a PyInstaller bundle
    base_path = sys._MEIPASS
else:
    # Running in a normal Python environment
    base_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))

# Carregar fontes com tamanhos ajustados para papel menor
try:
    font_path = os.path.join(base_path, "fonts", "Monospace.ttf")
    font_header = ImageFont.truetype(font_path, 18)      # Cabeçalho menor
    font_normal = ImageFont.truetype(font_path, 36)      # Produto: 24 + 50% = 36px
    font_price = ImageFont.truetype(font_path, 30)       # Preço: 26 + 50% = 39px
    print("Fontes carregadas com sucesso.")
except Exception as e:
    print("Erro ao carregar fonte:", e)
    font_header = ImageFont.load_default(size=18)
    font_normal = ImageFont.load_default(size=36)
    font_price = ImageFont.load_default(size=30)

# Desenhar fundo do cabeçalho (cinza claro)
draw.rectangle([(0, 0), (total_width, header_height)], fill=220, outline=0, width=3)

# Desenhar bordas principais
draw.rectangle([(0, 0), (total_width-1, total_height-1)], fill=None, outline=0, width=4)

# Desenhar linhas verticais
draw.line([(col1_width, 0), (col1_width, total_height)], fill=0, width=3)
draw.line([(col1_width + col2_width, 0), (col1_width + col2_width, total_height)], fill=0, width=3)

# Desenhar linha horizontal separando cabeçalho
draw.line([(0, header_height), (total_width, header_height)], fill=0, width=4)

# Desenhar linhas horizontais entre produtos
for i in range(1, len(produtos)):
    y = header_height + (i * row_height)
    draw.line([(0, y), (total_width, y)], fill=0, width=2)

# Adicionar texto do cabeçalho
header_y = (header_height - 18) // 2  # Ajustado para fonte menor
draw.text((padding, header_y), "PRODUTO", fill=0, font=font_header)

# Centralizar "QTD" na coluna
qtd_header_text = "QTD"
qtd_header_width = draw.textlength(qtd_header_text, font=font_header)
qtd_header_x = col1_width + ((col2_width - qtd_header_width) // 2)
draw.text((qtd_header_x, header_y), qtd_header_text, fill=0, font=font_header)

# Calcular posição centralizada para "PREÇO"
preco_text = "PREÇO"
preco_width = draw.textlength(preco_text, font=font_header)
preco_x = col1_width + col2_width + ((col3_width - preco_width) // 2)
draw.text((preco_x, header_y), preco_text, fill=0, font=font_header)

# Adicionar dados dos produtos com melhor formatação
for i, produto in enumerate(produtos):
    y_base = header_height + (i * row_height)
    text_y = y_base + ((row_height - 16) // 2)  # Ajustado para fonte menor

    # Nome do produto (alinhado à esquerda, truncado se necessário)
    nome_max_width = col1_width - (2 * padding)
    nome_text = produto['nome']
    if draw.textlength(nome_text, font=font_normal) > nome_max_width:
        # Truncar o nome se for muito longo
        while draw.textlength(nome_text + "...", font=font_normal) > nome_max_width and len(nome_text) > 1:
            nome_text = nome_text[:-1]
        nome_text += "..."
    draw.text((padding, text_y), nome_text, fill=0, font=font_normal)

    # Quantidade deixada em branco (não adiciona texto)

    # Preço (alinhado à direita)
    preco_width = draw.textlength(produto['preco'], font=font_price)
    preco_x = total_width - preco_width - padding
    draw.text((preco_x, text_y), produto['preco'], fill=0, font=font_price)


# Imprimir a imagem
printer.image(image)

# Finalizar e cortar o papel
printer.cut()
printer.close()

