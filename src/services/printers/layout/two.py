def _prepare_products(self) -> str:
    string = ""
    for product in self.products.values():
        if product.get("active"):
            name: str = product.get("name", "")
            if len(name) > 14:
                name = name[:14]
            name = name.ljust(14)
            price = f"R$ {product.get('price', 0):.2f}".ljust(10)
            string += f"{name} [ ][ ][ ][ ][ ][ ] {price}\n"

    return string


def get_weight_and_price(self) -> str:
    return f"""
Comanda: #{self.weighting_id}
Peso: {self.weight} kg
Preço: R$ {self.price:.2f}\n
"""


def get_products(self) -> str:
    products_text = self._prepare_products()
    if products_text != "":
        products_text = f"Produtos Adicionais:\n{products_text}"
    return products_text
