"""Módulo para salvar os dados de pesagem para uso pelo módulo de relatório."""

from datetime import datetime
import os
import csv

from utils.handle_path import get_config_path


def save_data(
    weight: str, total_price: str, kg_price: str,
    family_mode: int = 0, force_id: int = -1
) -> int:
    """
    Salva os dados de pesagem para uso pelo módulo de relatório.

    Gera um ID sequencial para cada registro dentro do arquivo diário.

    Parameters
    ----------
    weight : str
        Peso a ser salvo.
    total_price : str
        Preço total a ser salvo.
    kg_price : str
        Preço por kg a ser salvo.

    Returns
    -------
    int
        O ID do registro de pesagem salvo.
    """
    now = datetime.now()
    os.makedirs(f"{get_config_path()}/data/pesagens", exist_ok=True)
    file_path = f"{get_config_path()}/data/pesagens/{now.strftime('%d_%m_%Y')}.csv"

    current_id = 1  # Default ID for a new file or if no previous data

    # Check if file exists and determine the next ID
    if not os.path.exists(file_path):
        # File does not exist, create it and write header
        # Use newline='' to prevent extra blank rows in CSV
        with open(file_path, mode="w", encoding="utf-8", newline='') as file:
            writer = csv.writer(file)
            # Added "id_pesagem" as the last column
            writer.writerow(["momento", "peso", "preco", "preco_balanca", "id_pesagem", "familia"])
        # current_id remains 1 as it's the first record
    else:
        # File exists, try to find the last ID
        try:
            if force_id == -1:
                with open(file_path, mode="r", encoding="utf-8", newline='') as file:
                    reader = csv.reader(file)
                    rows = list(reader)  # Read all rows into a list
                    if len(rows) > 1:  # Check if there are data rows (more than just header)
                        last_row = rows[-1]
                        last_id_str = last_row[-2]
                        current_id = int(last_id_str) + 1
                    # If len(rows) <= 1, it means only header or empty file, so current_id remains 1
            else:
                current_id = force_id
        except (FileNotFoundError, IndexError, ValueError) as e:
            # Handle cases where file is malformed, empty, or ID is not an int
            # current_id remains 1 (default)
            print("Error getting the next id: %s", e)
            pass  # Or log the error if necessary

    # Now append the new data with the determined current_id
    with open(file_path, mode="a", encoding="utf-8", newline='') as file:
        writer = csv.writer(file)
        # Append the current_id to the data row
        writer.writerow(
            [
                now.isoformat(), weight, total_price, kg_price,
                current_id, family_mode
            ]
        )

    return current_id


def get_data_for_api(user_id: str) -> list[dict] | None:
    """
    Lê os arquivos de pesagens do diretório de dados e retorna uma lista de dicionários
    com os registros formatados para envio à API.

    Args:
        user_id (str): ID do usuário para associar aos registros.

    Returns:
        list[dict] | None: Lista de registros de pesagem ou None se houver apenas erros.
    """
    results = []
    has_errors = False
    for file in os.listdir(f"{get_config_path()}/data/pesagens"):
        today_data = datetime.now().strftime("%d_%m_%Y") + ".csv"
        if file == today_data:
            continue
        file_path = os.path.join(f"{get_config_path()}/data/pesagens", file)
        with open(file_path, mode="r", encoding="utf-8") as f:
            reader = csv.reader(f)
            next(reader, None)
            for row in reader:
                # Concatena a data do arquivo com o horário do registro
                try:
                    results.append(
                        {
                            "cliente_id": user_id,
                            "peso": int(str(row[1]).replace(".", "")),
                            "valor_balanca": float(row[3]),
                            "valor_cobrado": float(row[2]),
                            "momento_pesagem": row[0],
                        }
                    )
                except (IndexError, ValueError, FileNotFoundError):
                    has_errors = True
                    continue  # ignora linhas mal formatadas

    if has_errors and results == []:
        return None

    return results


def clear_data_folder():
    """
    Remove todos os arquivos do diretório de pesagens,
    garantindo que o diretório exista.

    Esta função é útil para limpar os dados locais após o envio para a API.
    """
    data_path = f"{get_config_path()}/data/pesagens"
    sent_path = f"{get_config_path()}/data/pesagens_enviadas"
    os.makedirs(sent_path, exist_ok=True)
    for file in os.listdir(data_path):
        today_data = datetime.now().strftime("%d_%m_%Y") + ".csv"
        if file == today_data:
            continue
        file_path = os.path.join(data_path, file)
        if os.path.isfile(file_path):
            os.rename(file_path, os.path.join(sent_path, file))


def get_today_data_as_json():
    """
    Retorna todos os dados de pesagens de hoje no formato:
    ```json
    [
        {
            "momento": "2025-07-08T21:43:36.080250",
            "peso": 12.345,
            "preco": 148.140,
            "preco_balanca": 12.000,
            "id_pesagem": 1,
            "familia": 1
        },
        ...
    ]
    ```
    """
    now = datetime.now()
    file_path = f"{get_config_path()}/data/pesagens/{now.strftime('%d_%m_%Y')}.csv"

    results = []

    if not os.path.exists(file_path):
        return results  # Retorna lista vazia se o arquivo do dia não existe

    try:
        with open(file_path, mode="r", encoding="utf-8", newline='') as file:
            # DictReader usa a primeira linha como chaves, o que simplifica o código
            reader = csv.DictReader(file)
            for row in reader:
                try:
                    # Converte os valores para os tipos corretos
                    formatted_row = {
                        "momento": row["momento"],
                        "peso": float(row["peso"]),
                        "preco": float(row["preco"]),
                        "preco_balanca": float(row["preco_balanca"]),
                        "id_pesagem": int(row["id_pesagem"]),
                        "familia": int(row["familia"])
                    }
                    results.append(formatted_row)
                except (ValueError, KeyError, TypeError):
                    # Ignora linhas malformadas ou com chaves ausentes
                    continue
    except FileNotFoundError:
        # Caso o arquivo seja deletado entre a verificação e a abertura
        return []

    return results


if __name__ == "__main__":
    # Example usage with the updated save_data signature
    print(f"Saved with ID: {save_data('10', '100', '10.0')}")
    print(f"Saved with ID: {save_data('20', '200', '10.0')}")
    print(f"Saved with ID: {save_data('30', '300', '10.0')}")
