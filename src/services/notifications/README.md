# Serviço de Notificações

Serviço para enviar notificações do sistema Pesa e Pronto para plataformas externas.

## Slack Integration

### Configuração

O serviço está configurado para enviar notificações para o webhook do Slack:
```
https://hooks.slack.com/triggers/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
```

### Funcionalidades

#### 1. Notificação de Shutdown Incorreto

Envia notificação quando o sistema detecta que não foi desligado corretamente.

**Payload enviado:**
```json
{
  "nome_cliente": "Nome do Cliente"
}
```

#### 2. Notificações Customizadas

Permite enviar notificações customizadas com qualquer payload.

### Como Usar

#### Notificação de Shutdown Incorreto

```python
from services.notifications.slack import SlackNotificationService

slack_service = SlackNotificationService()
success = slack_service.send_improper_shutdown_notification("Nome do Cliente")
```

#### Notificação Customizada

```python
from services.notifications.slack import SlackNotificationService

slack_service = SlackNotificationService()
payload = {
    "nome_cliente": "Restaurante ABC",
    "tipo_evento": "shutdown_incorreto",
    "timestamp": "2025-08-13T22:44:27"
}
success = slack_service.send_custom_notification(payload)
```

#### Teste do Webhook

```python
from services.notifications.slack import SlackNotificationService

slack_service = SlackNotificationService()
is_working = slack_service.test_webhook()
```

### Integração com ShutdownService

O `ShutdownService` automaticamente envia notificações para o Slack quando detecta shutdown incorreto:

1. **Sistema inicia** → Verifica shutdown anterior
2. **Shutdown incorreto detectado** → Envia notificação para Slack
3. **Obtém nome do cliente** → Da configuração do sistema
4. **Envia notificação** → Com nome do cliente

### Obtenção do Nome do Cliente

O sistema obtém o nome do cliente da configuração:

```python
from services.shutdown.core import get_client_name

nome_cliente = get_client_name()
```

**Prioridade:**
1. `nome_cliente` da configuração
2. `nome_restaurante` como fallback
3. "Cliente Desconhecido" se nenhum encontrado

### Configuração do Cliente

Para que as notificações incluam o nome correto, configure o cliente no sistema:

```json
{
  "config_client": {
    "nome_cliente": "João Silva",
    "nome_restaurante": "Restaurante do João",
    "endereco": "Rua das Flores, 123",
    "frequencia_relatorio": "diario"
  }
}
```

### Tratamento de Erros

O serviço trata os seguintes erros:

- **Conexão**: Timeout, DNS, rede
- **HTTP**: Status codes diferentes de 200
- **Webhook**: Workflow não publicado, URL inválida
- **Cliente**: Nome não encontrado (usa fallback)

### Logs

O serviço emite logs detalhados:

- **INFO**: Notificações enviadas com sucesso
- **ERROR**: Falhas de conexão, HTTP, webhook
- **WARNING**: Nome do cliente não encontrado

### Troubleshooting

#### Erro "workflow_not_published"

```json
{"ok":false,"error":"workflow_not_published"}
```

**Solução**: O workflow do Slack precisa ser publicado/ativado.

#### Timeout de Conexão

**Solução**: Verificar conectividade com a internet e firewall.

#### Nome do Cliente "Cliente Desconhecido"

**Solução**: Configurar `config_client` no sistema com `nome_cliente` ou `nome_restaurante`.

### Exemplo Completo

```python
import logging
from services.shutdown.core import ShutdownService

# Configura logging
logging.basicConfig(level=logging.INFO)

# Inicializa ShutdownService (já integrado com Slack)
shutdown_service = ShutdownService()

# Registra startup (verifica shutdown anterior automaticamente)
shutdown_service.register_startup()

# Se shutdown anterior foi incorreto, notificação será enviada automaticamente
```

### Testes

Execute o script de teste:

```bash
python test_slack_integration.py
```

O teste verifica:
- Conectividade com webhook
- Obtenção do nome do cliente
- Envio de notificações
- Integração completa com ShutdownService
