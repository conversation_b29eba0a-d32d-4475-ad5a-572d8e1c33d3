"""Módulo para enviar notificações para o Slack."""

import logging
import requests
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


class SlackNotificationService:
    """
    Serviço para enviar notificações para o Slack via webhook.
    """

    def __init__(self, webhook_url: Optional[str] = None):
        """
        Inicializa o service de notificações do Slack.

        Args:
            webhook_url: URL do webhook do Slack. Se não fornecida, usa a URL padrão.
        """
        self.webhook_url = webhook_url or "https://hooks.slack.com/triggers/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"

    def send_improper_shutdown_notification(self, nome_cliente: str) -> bool:
        """
        Envia notificação de shutdown incorreto para o Slack.

        Args:
            nome_cliente: Nome do cliente que teve shutdown incorreto

        Returns:
            bool: True se a notificação foi enviada com sucesso, False caso contrário
        """
        try:
            payload = {
                "nome_cliente": nome_cliente
            }

            logger.info("Enviando notificação de shutdown incorreto para o Slack: %s", nome_cliente)

            response = requests.post(
                self.webhook_url,
                json=payload,
                timeout=10
            )

            if response.status_code == 200:
                logger.info("Notificação enviada com sucesso para o Slack")
                return True
            else:
                logger.error(
                    "Erro ao enviar notificação para o Slack. Status: %s, Response: %s",
                    response.status_code,
                    response.text
                )
                return False

        except requests.exceptions.RequestException as e:
            logger.error("Erro de conexão ao enviar notificação para o Slack: %s", e)
            return False
        except Exception as e:
            logger.error("Erro inesperado ao enviar notificação para o Slack: %s", e)
            return False

    def send_custom_notification(self, payload: Dict[str, Any]) -> bool:
        """
        Envia notificação customizada para o Slack.

        Args:
            payload: Dados a serem enviados no webhook

        Returns:
            bool: True se a notificação foi enviada com sucesso, False caso contrário
        """
        try:
            logger.info("Enviando notificação customizada para o Slack: %s", payload)

            response = requests.post(
                self.webhook_url,
                json=payload,
                timeout=10
            )

            if response.status_code == 200:
                logger.info("Notificação customizada enviada com sucesso para o Slack")
                return True
            else:
                logger.error(
                    "Erro ao enviar notificação customizada para o Slack. Status: %s, Response: %s",
                    response.status_code,
                    response.text
                )
                return False

        except requests.exceptions.RequestException as e:
            logger.error("Erro de conexão ao enviar notificação customizada para o Slack: %s", e)
            return False
        except Exception as e:
            logger.error("Erro inesperado ao enviar notificação customizada para o Slack: %s", e)
            return False

    def test_webhook(self) -> bool:
        """
        Testa se o webhook está funcionando.

        Returns:
            bool: True se o webhook está funcionando, False caso contrário
        """
        try:
            test_payload = {
                "nome_cliente": "TESTE_WEBHOOK"
            }

            logger.info("Testando webhook do Slack")

            response = requests.post(
                self.webhook_url,
                json=test_payload,
                timeout=10
            )

            if response.status_code == 200:
                logger.info("Webhook do Slack está funcionando corretamente")
                return True
            else:
                logger.error(
                    "Webhook do Slack não está funcionando. Status: %s, Response: %s",
                    response.status_code,
                    response.text
                )
                return False

        except requests.exceptions.RequestException as e:
            logger.error("Erro de conexão ao testar webhook do Slack: %s", e)
            return False
        except Exception as e:
            logger.error("Erro inesperado ao testar webhook do Slack: %s", e)
            return False
