import logging

import asyncio
import websockets
import websockets.server
from datetime import datetime

from config.core import Config
from controllers.state.core import GlobalState
from services.check.core import Check
from services.check.structure import CheckStructure
from services.files.core import Files
from services.report.record import save_data

logger = logging.getLogger(__name__)


def send_paper_status_mesage(
    status: int,
    websocket: websockets.server.WebSocketServerProtocol,
    loop: asyncio.AbstractEventLoop
):
    match status:
        case 1:
            message = {
                "message": "O papel está quase acabando!"
            }
        case 0:
            message = {
                "message": "O papel acabou!"
            }
        case _:
            return

    future = asyncio.run_coroutine_threadsafe(websocket.send(message), loop)
    result = future.result()
    if result:
        logger.warning(result)


def _get_code_from_weight_map(
    weight_map: list, weight_state: dict | None
) -> None | str:
    for item in weight_map:
        if (
            weight_state and float(
                item.get("valorMapeado", None)
            ) == float(weight_state["preco_kg"])
        ):
            return item.get("codigoChamada", None)
    return None


def _get_barcode_list_from_family_mode_state(
    weight_map: list, family_mode_state: list
) -> list:
    matched_weights = {}
    for item in weight_map:
        mapped_value = float(item.get("valorMapeado", ""))
        matched_weights[mapped_value] = []

    total_weight_of_unmatched_weights = .0
    for state in family_mode_state:
        if matched_weights.get(
            float(state["preco_kg"]), None
        ) is not None:
            matched_weights[float(state["preco_kg"])].append(
                float(state["peso"])
            )
        else:
            total_weight_of_unmatched_weights += float(state["peso"])

    weight_map_dict = {}
    for item in weight_map:
        mapped_value = float(item.get("valorMapeado", ""))
        weight_map_dict[mapped_value] = item.get("codigoChamada")

    codes = []
    for k, v in matched_weights.items():
        if len(v) == 0:
            continue
        total_weight_for_code = .0
        for w in v:
            total_weight_for_code += w
        code = f"{weight_map_dict[k]}\r{total_weight_for_code:.3f}"
        codes.append(code)

    if total_weight_of_unmatched_weights > .0:
        codes.append(
            f"{total_weight_of_unmatched_weights:.3f}"
        )

    return codes


def _must_not_print_because_of_family_mode(
    global_state: GlobalState, cut_family_mode: int, weight_state: dict
) -> bool:
    if (
        (active_family_mode := global_state.weighting.active_family_mode)
        and not cut_family_mode
        and weight_state
    ):
        logger.info(active_family_mode)
        logger.info(type(active_family_mode))
        global_state.weighting.family_mode[active_family_mode]["weights"].append(
            weight_state
        )
        global_state.send_serial_data = True
        return True
    return False


def make_print(
    config: Config,
    websocket: websockets.server.WebSocketServerProtocol,
    loop: asyncio.AbstractEventLoop,
    global_state: GlobalState,
    weight_state: dict,
    cut_family_mode: int = 0,
    force_layout: str | None = None
):
    """
    Função para fazer a impressão.
    """
    global_state.send_serial_data = False
    logger.info("Peso estável. Aguardando nova oscilação.")
    global_state.weighting.clear_weights()

    # Buscando as configurações
    products = Config().products or {}

    config_client = (
        Files().get("config_client") or {}
    ).get("data") or {}
    config_preferences = (
        Files().get("config_preferences") or {}
    ).get("data") or {}

    config_printer = config_preferences.get("impressao") or {}
    config_product = config_preferences.get("produto") or {}

    barcode_mode = config_printer.get(
        "configBarcode", {}
    ).get("ativarCodigoBarras", False)

    barcode_type = config_printer.get(
        "configBarcode", {}
    ).get("tipoCodigoBarras", {}).get(
        "type", "CODE128"
    )

    layout = str(
        config_printer.get("layoutSelecionado", {}).get("value", 1)
    ) if not force_layout else force_layout

    activate_blank_lines = config_product.get(
        "ativarPesoEmBranco", False
    )
    blank_lines = config_product.get(
        "numeroLinhasEmBranco", 0
    ) if activate_blank_lines else 0

    weight_map = config_printer.get(
        "mapaPreco", {}
    ).get("mapaPreco", [])
    activate_weight_map = config_printer.get(
        "mapaPreco", {}
    ).get("ativarMapaPreco", False)

    logger.info("weight_map=%s", weight_map)

    # -------------------------

    # Preparando dados para a impressão

    code = _get_code_from_weight_map(weight_map, weight_state)

    if _must_not_print_because_of_family_mode(
        global_state, cut_family_mode, weight_state
    ):
        return

    if cut_family_mode:
        # assume que weight_state veio vazio
        family_mode_state: list = global_state.weighting.family_mode[cut_family_mode]["weights"]
        global_state.weighting.clear_family_mode(cut_family_mode)

        if not len(family_mode_state):
            return

        weight = .0
        price = .0

        temp_state = family_mode_state.copy()
        # Já podemos salvar todas as pesagens com o mesmo id
        first_state = temp_state.pop(0)
        weighting_id = save_data(
            weight=first_state["peso"],
            total_price=first_state["preco"],
            kg_price=first_state["preco_kg"],
            family_mode=cut_family_mode,
        )
        for state in temp_state:
            _ = save_data(
                weight=state["peso"],
                total_price=state["preco"],
                kg_price=state["preco_kg"],
                family_mode=cut_family_mode,
                force_id=weighting_id
            )
        del temp_state

    elif weight_state:
        family_mode_state = []
        weight = weight_state["peso"]
        price = float(weight_state["preco"])
        weighting_id = save_data(
            weight=weight_state["peso"],
            total_price=weight_state["preco"],
            kg_price=weight_state["preco_kg"]
        )

    else:
        return

    product_code = None
    product_codes = []

    if weight_state:
        if code and activate_weight_map:
            product_code = f"{code}\r{weight_state["peso"]}"
        elif barcode_mode and not activate_weight_map:
            product_code = f"{weight_state["peso"]}"

    elif cut_family_mode:
        product_codes = _get_barcode_list_from_family_mode_state(
            weight_map, family_mode_state
        )

    # --------------------------

    # Construindo objetos base

    structure = CheckStructure(
        company=config_client.get("nome_restaurante", ""),
        check_time=datetime.now().strftime("%d/%m/%Y %H:%M"),
        weight=weight,
        price=price,
        products=products,
        weighting_id=weighting_id,
        blank_lines=blank_lines,
        family_mode_state=family_mode_state
    )

    check = Check(structure=structure)
    structure.preview()

    # --------------------------

    # Impressão (retorna status de papel da impressora)
    paper_status = check.print_check(
        printer=config.usb_printer,
        barcode_mode=barcode_mode,
        barcode_type=barcode_type,
        product_code=product_code,
        product_codes=product_codes,
        layout=layout,
        state=global_state
    ) or 2
    if paper_status < 2:
        send_paper_status_mesage(
            paper_status, websocket, loop
        )

    global_state.send_serial_data = True
