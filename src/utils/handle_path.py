import os

from models import Request


def resolve_path(request: Request) -> str | None:
    if len(request.path.split("_")) > 1:
        return request.path.split("_")[0]
    return None


def get_config_path() -> str:
    """
    Retorna o caminho absoluto do diretório de configuração do sistema para o app.
    """
    app_name: str = "pesa_e_pronto"
    xdg_config_home = os.environ.get("XDG_CONFIG_HOME", os.path.expanduser("~/.config"))
    config_path = os.path.join(xdg_config_home, app_name)
    os.makedirs(config_path, exist_ok=True)
    return config_path
