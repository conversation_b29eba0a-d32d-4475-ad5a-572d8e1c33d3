"""Entrypoint da aplicação"""


def is_running():
    import os
    import subprocess

    port = os.getenv("WEBSOCKET_PORT", "6789")
    result = subprocess.run(["lsof", "-i", f":{port}"], stdout=subprocess.DEVNULL)
    return result.returncode == 0


if __name__ == "__main__":
    import logging
    import os
    import sys
    from argparse import ArgumentParser
    from services.api.core import Api

    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
    )

    logging.info("Setting up environment")
    if getattr(sys, "frozen", False):
        # Executando via PyInstaller
        base_path = sys._MEIPASS
    else:
        # Executando via fonte
        base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    sys.path.insert(0, base_path)

    parser = ArgumentParser(description="Linhas de comando para balança smart")
    parser.add_argument(
        "--send_data",
        action="store_true",
        help="Enviar os dados para a API Pesa e Pronto",
    )

    args = parser.parse_args()

    api = Api()

    if args.send_data:
        logging.info("Enviando dados...")
        api.send_data()
        sys.exit(0)

    if is_running():
        logging.info("Aplicação já está em execução!")
        sys.exit(1)

    import asyncio
    from controllers.socket.socket_listener import listen_socket

    asyncio.run(listen_socket())
