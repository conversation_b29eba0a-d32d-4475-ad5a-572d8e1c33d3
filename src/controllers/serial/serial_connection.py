"""Módulo para conexão serial."""

import logging

import serial
from serial import SerialException

logger = logging.getLogger(__name__)


class SerialConnection:
    """
    Classe Singleton para conexão serial.

    Attributes:
    ----------
    ser: SerialConnection
        Instância da conexão serial.
    """

    _ser = None
    _port = None
    _connected = False

    def __init__(self) -> None:
        pass

    @classmethod
    def get_serial_connection(cls) -> serial.Serial:
        """Retorna a instância da conexão serial."""
        if cls._ser is None:
            return None
        return cls._ser

    @classmethod
    def connect(
        cls, port: str = None, baudrate: int = 2400, timeout: float = 1.0
    ) -> None:
        """Estabelece a conexão serial."""
        if port is None:
            raise SerialException("Porta serial não pode ser None!")
        if cls._connected:
            cls.disconnect()
        cls._ser = serial.Serial(port, baudrate, timeout=timeout)
        cls._port = port
        logging.info("Connected to %s at %s baud.", port, baudrate)

    @classmethod
    def disconnect(cls):
        """Fecha a conexão serial."""
        if cls._ser is not None:
            cls._ser.close()
            logging.info("Serial connection closed.")
        elif "ser" in locals():
            locals()["ser"].close()
        else:
            logging.warning("No serial connection to close.")

    @classmethod
    def reconnect(cls):
        """Reconecta à porta serial."""
        cls.disconnect()
        cls.connect(cls._port)
