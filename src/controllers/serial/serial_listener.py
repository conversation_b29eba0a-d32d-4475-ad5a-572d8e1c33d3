"""Módeulo para leitura de dados da porta serial."""

import time
import logging
import serial

from controllers.state.core import GlobalState
from models import Page

logger = logging.getLogger(__name__)


def listen_port(
    ser: serial.Serial, callback: callable, state: GlobalState
):
    """
    Recebe uma conexão serial e aguarda dados.

    Parameters:
    ----------
    ser: serial.Serial
        Objeto serial.Serial configurado.
    callback: function
        Função de callback para tratar os dados recebidos.
    """
    try:
        if ser is None:
            callback("error")
        # ser = serial.Serial(port=porta, baudrate=baudrate, timeout=timeout)
        porta = ser.port
        baudrate = ser.baudrate
        logger.info("Conectado à porta %s com baudrate %s", porta, baudrate)

        if not ser.is_open:
            raise serial.SerialException(f"Porta {porta} não está aberta.")
        logger.info("Porta %s aberta.", porta)

        no_response_count = 0  # Contador para ciclos sem resposta

        while True:
            if state.page != Page.FLOW:
                time.sleep(0.15)  # Aguarda a resposta
                continue
            ser.write(b"\x05")  # Envia o comando ENQ
            time.sleep(0.15)  # Aguarda a resposta

            if ser.in_waiting:
                line = ser.readline().decode("ascii", errors="ignore").strip()
                line = line.replace("\x02", "").replace("\x03", "").strip()
                if callback:
                    logger.info("Dado recebido da serial: '%s'", line)
                    callback(line)
                no_response_count = 0  # Reseta contador quando há resposta
            else:
                no_response_count += 1
                if no_response_count >= 3:
                    logger.warning("Sem resposta por 3 ciclos consecutivos")
                    callback("00000   00000   00000")
                    no_response_count = 0  # Reseta após notificar

    except (
        serial.serialutil.PortNotOpenError, serial.SerialException, OSError
    ) as e:
        logger.error("Erro ao conectar na porta %s: %s", porta, e)
    except KeyboardInterrupt:
        logger.warning("Interrompido pelo usuário.")
    finally:
        if "ser" in locals() and ser.is_open:
            ser.close()
            logger.warning("Porta %s fechada.", porta)
        callback("error")
