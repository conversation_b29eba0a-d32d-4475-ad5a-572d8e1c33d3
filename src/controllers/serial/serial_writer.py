import serial
import os
import time
import logging

from src.services.check.structure import CheckStructure

logger = logging.getLogger(__name__)


def write_port(dados: CheckStructure, port=None):
    try:
        if port is None:
            port = os.getenv("PRINTER_SERIAL_PORT", "/dev/ttys002")
        baudrate = os.getenv("STD_BAUDRATE", 9600)

        # Inicializa a conexão serial
        ser = serial.Serial(port=port, baudrate=baudrate, timeout=1)
        logger.info(f"Conectado à porta {port} com baudrate {baudrate}")

        # Aguarda um momento para estabilizar a conexão
        time.sleep(2)

        ser.write(dados.get_header().encode("utf-8"))
        ser.write(dados.get_weight_and_price().encode("utf-8"))
        ser.write(dados.get_products().encode("utf-8"))
        ser.write(dados.get_footer().encode("utf-8"))
        logger.info(f"Dados enviados: {dados}")

    except serial.SerialException as e:
        logger.error(f"Erro ao conectar na porta {port}: {e}")
    except KeyboardInterrupt:
        logger.error("Interrompido pelo usuário.")
    finally:
        if "ser" in locals() and ser.is_open:
            ser.close()
            logger.info(f"Porta {port} fechada.")
