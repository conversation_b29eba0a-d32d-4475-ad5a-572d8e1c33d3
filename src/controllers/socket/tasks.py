"""<PERSON><PERSON><PERSON><PERSON> para tarefas assíncronas do client websocket."""

import logging
import os
import json

import asyncio
import websockets
import websockets.server
from serial import SerialException

from config.core import Config
from controllers.serial.serial_connection import SerialConnection
from controllers.serial.serial_listener import listen_port
from controllers.state.core import GlobalState
from models import Request, WeightingStatus, Action, Page, CommandPath
from services.api.core import Api
from services.files.core import Files
from utils.handle_path import resolve_path
from utils.handle_print import make_print

logger = logging.getLogger(__name__)


async def read_serial_data(
    websocket: websockets.server.WebSocketServerProtocol,
    loop: asyncio.AbstractEventLoop,
    serial_connection: SerialConnection,
    state: GlobalState
) -> None:
    """
    Método que lê os dados da porta serial e envia para o WebSocket.

    Parameters:
    ----------
    websocket: websockets.server.WebSocketServerProtocol
        Objeto de conexão WebSocket.
    loop: asyncio.AbstractEventLoop
        Loop de eventos.
    weighting: Weighting
        Objeto de controle de pesagem.
    """
    if state.read_serial_data_task:
        return
    logger.info("Inicializando leitura de dados seriais")
    state.read_serial_data_task = True
    config = Config()

    def callback(line: str) -> None:
        """
        Método de callback que é chamado quando um novo dado é recebido da porta serial.

        Parameters:
        ----------
        line: str
            Dado recebido da porta serial.

        Raises:
        ------
        ValueError
            Se o campo 'peso' não for encontrado no JSON recebido.
        JSONDecodeError
            Se houver erro ao decodificar o JSON recebido.
        """
        nonlocal serial_connection
        nonlocal state
        nonlocal config
        if not state.send_serial_data:
            return
        try:

            if line == "error":
                serial_connection.reconnect()
                return

            if websocket.closed:
                serial_connection.disconnect()
                return

            state.weighting.add_weight(line)
            weight_state = state.weighting.current_state()

            state_json = json.dumps(weight_state)
            future = asyncio.run_coroutine_threadsafe(
                websocket.send(state_json), loop
            )
            result = future.result()
            if result:
                logger.warning(result)
            logger.info("Enviado ao websocket: %s", state_json)

            if weight_state["status"] == WeightingStatus.STABLE.value:
                make_print(config, websocket, loop, state, weight_state)

        except json.JSONDecodeError as e:
            logger.error("Erro ao decodificar JSON: %s, dados: %s", e, line)
        except ValueError as e:
            logger.error("Erro ao processar dados: %s, dados: %s", e, line)
        except Exception as e:  # pylint: disable=broad-except
            import traceback
            traceback.print_exc()
            logger.error("Erro inesperado no callback: %s, dados: %s", e, line)

    while True:
        while True:
            config = Config()
            port = config.usb_scaler
            baudrate = int(os.getenv("STD_BAUDRATE", "2400"))
            timeout = int(os.getenv("STD_TIMEOUT", "1"))
            try:
                serial_connection.disconnect()
                serial_connection.connect(
                    port=port, baudrate=baudrate, timeout=timeout
                )

                ser = serial_connection.get_serial_connection()
                break
            except SerialException as e:
                logger.error(str(e))
                logger.info(
                    f"Tentando reconectar à balança {port} em 5 segundos..."
                )
                await asyncio.sleep(5)
                continue

        try:
            await asyncio.to_thread(
                listen_port, ser=ser, callback=callback, state=state
            )
        except Exception:
            continue


async def set_config(
    websocket: websockets.server.WebSocketServerProtocol,
    loop: asyncio.AbstractEventLoop,
    state: GlobalState
) -> None:
    """
    Método que valida os arquivos de configuração e envia para o WebSocket.

    Parameters:
    ----------
    websocket: websockets.server.WebSocketServerProtocol
        Objeto de conexão WebSocket.
    loop: asyncio.AbstractEventLoop
        Loop de eventos.

    Sends:
    ------
    response: dict
        Resposta da configuração.
    """
    if state.set_config_task:
        return
    logger.info("Inicializando validação de configuração")
    state.set_config_task = True

    config = Config()
    response = config.validate_config()
    if config.from_file:
        logger.info("Sistema já inicializado a partir de arquivos.")
        state.page = Page.FLOW
        response = {
            "action": Action.COMMAND.value,
            "path": CommandPath.FLOW.value,
        }
    logger.info("Validação do Config/Resposta WebSosket: %s", response)
    # envia a resposta para o websocket
    asyncio.run_coroutine_threadsafe(
        websocket.send(json.dumps(response)), loop
    )


async def process_data(
    websocket: websockets.server.WebSocketServerProtocol,
    message: str,
    state: GlobalState
) -> None:
    """
    Método para gerenciar a transmissão de dados entre o cliente e o servidor.

    Parameters:
    ----------
    websocket: websockets.server.WebSocketServerProtocol
        Objeto de conexão WebSocket.
    message: str
        Mensagem recebida do cliente.
    """
    try:
        data = json.loads(message)
        request = Request(**data)

        response = None

        # Retorna done apenas se a única função do request for
        # modificar o estado
        state_response = state.modify_based_on_request(request)

        should_return = False

        if partial_responses := state_response.pop("responses"):
            for response in partial_responses:
                await websocket.send(json.dumps(response))
            if len(partial_responses) > 0:
                should_return = True

        if state_response.pop("ok"):
            await websocket.send(json.dumps(state_response))
            return

        if should_return:
            return


        if "email" in request.path and request.path != 'send_email':
            api = Api()
            try:
                response = api.handle_email(request)
            except ConnectionError:
                response = {
                    "status": "error",
                    "message": "Problema de conexão!"
                }

        elif request.action == Action.COMMAND and not resolve_path(request):
            api = Api()
            response = api.process_request(request.path)

        elif request.action == Action.COMMAND and resolve_path(request) == "printer":
            action = request.path.removeprefix("printer_")
            match action:
                case "model":
                    sample_state = {
                        "peso": "0.5",
                        "preco": "29.95",
                        "preco_kg": "59.90",
                        "status": 3, # Estável
                    }
                    make_print(
                        config=Config(),
                        websocket=websocket,
                        loop=asyncio.get_running_loop(),
                        global_state=state,
                        weight_state=sample_state,
                        force_layout="1"
                    )
                    make_print(
                        config=Config(),
                        websocket=websocket,
                        loop=asyncio.get_running_loop(),
                        global_state=state,
                        weight_state=sample_state,
                        force_layout="2"
                    )

        elif request.action == Action.COMMAND and resolve_path(request) == "mode":
            resolved_path = request.path.removeprefix("mode_family_cut_")
            family = 0
            match resolved_path:
                case "one": family = 1
                case "two": family = 2

            if family:
                make_print(
                    config=Config(),
                    websocket=websocket,
                    loop=asyncio.get_running_loop(),
                    global_state=state,
                    weight_state={},
                    cut_family_mode=family
                )
            return


        elif request.path == "shutdown_computer":
            logger.info("Desligando computador!!!")
            os.system("poweroff")

        else:
            files = Files()
            api = Api()
            response = files.request(request=request)
            api_response = api.sync_request(request=request)
            if api_response:
                response = api_response

        if response:
            await websocket.send(json.dumps(response))
        else:
            # Caso contrário, retorna um erro
            await websocket.send(
                json.dumps({"status": "error", "message": message})
            )

    except json.JSONDecodeError as e:
        # Se não for JSON, retorna um erro
        await websocket.send(
            json.dumps({"status": "error", "message": str(e)})
        )
    except Exception as e:  # pylint: disable=broad-except
        import traceback

        traceback.print_exc()
        logger.error("Erro ao processar mensagem: %s", e)
        await websocket.send(
            json.dumps({"status": "error", "message": str(e)})
        )


# async def monitor_tasks():
#     import random
#     pid = random.randint(1000, 9999)
#     while True:
#         print(f"\n--- Monitorando Tarefas Assíncronas - {pid} ---")
#         tasks = asyncio.all_tasks()
#         for task in tasks:
#             print(
# f"Tarefa: {task.get_name()}, Estado: {task.get_coro().__name__}, Concluída: {task.done()}")
#         print("--- Fim da Monitoria ---\n")
#         await asyncio.sleep(5)
