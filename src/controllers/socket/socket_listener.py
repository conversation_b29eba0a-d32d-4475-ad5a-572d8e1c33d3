import asyncio
import logging
import os
import websockets
import websockets.server
from functools import partial

from .conn_handler import handler
from controllers.state.core import GlobalState
from services.files.core import Files

logger = logging.getLogger(__name__)


async def listen_socket(testing: bool = False):
    logger.info("Running WebSocket listener")
    host = os.getenv("WEBSOCKET_HOST", "localhost")
    port = int(os.getenv("WEBSOCKET_PORT", 6789))
    logger.info(f"Starting server on {host}:{port}")

    state = GlobalState()

    preferences = Files().get(path="config_preferences")

    if preferences:
        state.setup_weighting_configuration(
            config=(preferences.get("data") or {}).get("pesagem")
        )

    state.testing = testing

    stateful_handler = partial(handler, state=state)

    # Config do websockets
    async with websockets.server.serve(stateful_handler, host, port):
        logger.info(f"Servidor WebSocket rodando em %s:%s", host, port)
        await asyncio.Future()  # run forever
