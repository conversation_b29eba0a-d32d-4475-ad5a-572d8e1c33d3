import logging
from typing import Set, Coroutine, Any

import asyncio
import websockets
import websockets.server

logger = logging.getLogger(__name__)


class WebSocketManager:
    """
    Gerenciador para lidar com tarefas associadas a uma conexão WebSocket.

    Este gerenciador encapsula a lógica de gerenciamento de tarefas,
    garantindo que todas as tarefas relacionadas a uma conexão WebSocket
    sejam encerradas adequadamente quando a conexão é fechada.

    Attributes:
    ----------
    websocket : websockets.server.WebSocketServerProtocol
        Objeto representando a conexão WebSocket atual.
    tasks : Set[asyncio.Task]
        Conjunto de tarefas ativas associadas a esta conexão.
    """

    def __init__(self, websocket: websockets.server.WebSocketServerProtocol) -> None:
        """
        Inicializa o gerenciador com uma conexão WebSocket.

        Parameters:
        ----------
        websocket : websockets.server.WebSocketServerProtocol
            Conexão WebSocket para gerenciar.
        """
        self.websocket: websockets.server.WebSocketServerProtocol = websocket
        self.tasks: Set[asyncio.Task] = set()

    async def add_task(self, coro: Coroutine[Any, Any, None]) -> asyncio.Task:
        """
        Adiciona uma tarefa ao conjunto de tarefas gerenciadas.

        Parameters:
        ----------
        coro : Coroutine[Any, Any, None]
            Corrotina a ser adicionada como tarefa.

        Returns:
        -------
        asyncio.Task
            A tarefa criada a partir da corrotina fornecida.
        """
        task = asyncio.create_task(coro)
        self.tasks.add(task)
        logger.info(f"Tarefa adicionada: {task}")
        return task

    async def cleanup(self) -> None:
        """
        Cancela todas as tarefas associadas e aguarda sua finalização.

        Garante que nenhuma tarefa relacionada à conexão WebSocket permaneça ativa
        após o encerramento da conexão.
        """
        logger.info("Limpando tarefas associadas ao WebSocket")
        for task in self.tasks:
            logger.info(f"Cancelando tarefa: {task}")
            task.cancel()
        await asyncio.gather(*self.tasks, return_exceptions=True)
        logger.info("Tarefas limpas com sucesso")
