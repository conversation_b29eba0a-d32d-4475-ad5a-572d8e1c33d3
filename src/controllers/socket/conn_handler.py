"""Módulo de controle de cone<PERSON>ão WebSocket."""

import logging
import asyncio
import websockets
import websockets.server
import websockets.exceptions

from controllers.serial.serial_connection import SerialConnection
from controllers.state.core import GlobalState
from .tasks import (
    read_serial_data,
    set_config,
    process_data,
    # monitor_tasks
)
from .websocket_manager import WebSocketManager

logger = logging.getLogger(__name__)


async def handler(
    websocket: websockets.server.WebSocketServerProtocol, state: GlobalState
):
    """Handler da conexão WebSocket."""
    state.modify_based_on_new_connection()
    manager = WebSocketManager(websocket)
    try:
        await manager.add_task(set_config(websocket, asyncio.get_running_loop(), state=state))

        serial_connection = SerialConnection()
        await manager.add_task(
            read_serial_data(
                websocket,
                asyncio.get_running_loop(),
                serial_connection,
                state
            )
        )

        async for message in websocket:
            logger.info("Recebido do websocket: %s", message)
            await manager.add_task(process_data(websocket, message, state))

    except websockets.exceptions.ConnectionClosedOK:
        logger.info("Connection closed normally")
    except websockets.exceptions.ConnectionClosedError as e:
        logger.error("Connection closed with error: %s", e)
    finally:
        state.set_config_task = False
        serial_connection.disconnect()
        del serial_connection
        await manager.cleanup()
        # logger.info("-----------------------HOT RELOAD-----------------------")
        # os.execv(sys.executable, [sys.executable] + sys.argv)
