import logging

from models import Page, Request
from services.weighting.core import Weighting
from utils.handle_path import resolve_path

logger = logging.getLogger(__name__)
logger.handlers = []
logger.propagate = False
handler = logging.StreamHandler()
handler.setFormatter(
    logging.Formatter(fmt="%(asctime) - s%(levelno)s - [STATE] - %(message)s")
)
logger.addHandler(handler)


class GlobalState:
    _instance = None
    _initialized = False

    def __init__(self):
        if self._initialized:
            return

        logger.info("\n\nInstância do GlobalState sendo criada!\n\n")
        self.read_serial_data_task = False
        self.set_config_task = False
        self.send_serial_data = True
        self.testing = False
        self.page: Page = Page.LOAD
        self.weighting: Weighting = Weighting()
        self._initialized = True
        self.unlocked = False

    def __setattr__(self, name, value):
        if self._initialized:
            logger.info("=" * 20)
            logger.info("Estado sendo modificado!")
            logger.info("Atributo: %s", name)
            logger.info("Valor anterior: %s", getattr(self, name, "Valor inválido!"))
            logger.info("Valor novo: %s", value)
            logger.info("=" * 20)
        super().__setattr__(name, value)

    def __new__(cls) -> "GlobalState":
        if cls._instance is None:
            cls._instance = super(GlobalState, cls).__new__(cls)
        return cls._instance

    def modify_based_on_new_connection(self) -> None:
        self.read_serial_data_task = False
        self.set_config_task = False
        self.send_serial_data = True

    def setup_weighting_configuration(self, config: dict) -> None:
        if not config:
            return
        t = config.get("numeroThreshold", 0)
        p = (
            config.get("configPrecoViaSoftware", {})
            .get("atalhosPreco", [])[0]
            .get("preco", None)
        )
        self.weighting.set_threshold(t)
        if p:
            self.weighting.predefined_base_price = p
            self.weighting.set_predefined_price(True)

    def modify_based_on_request(self, request: Request) -> dict:
        response = {"ok": False}

        if request.path == "config_preferences" and request.data:
            p = (
                request.data.get("pesagem", {})
                .get("configPrecoViaSoftware", {})
                .get("atalhosPreco", [])[0]
                .get("preco", None)
            )
            t = request.data.get("pesagem", {}).get("numeroThreshold", 0)
            self.weighting.predefined_base_price = p
            self.weighting.set_threshold(t)
            response["ok"] = False

        elif resolve_path(request) == "go":
            try:
                self.page = Page(request.path.removeprefix("go_"))
                response = {
                    "status": "success",
                    "message": "Mudança de página registrada!",
                }
            except ValueError as e:
                logger.info(f"Erro ao registrar mudança de página: {e}")
                response = {"status": "error", "message": "Página inválida!"}

            try:
                self.page = Page(request.path.removeprefix("go_"))
                response = {
                    "status": "success",
                    "message": "Mudança de página registrada!",
                }
            except ValueError as e:
                logger.info(f"Erro ao registrar mudança de página: {e}")
                response = {"status": "error", "message": "Página inválida!"}

            response["ok"] = True

        response["responses"] = []

        weighting_responses = self.weighting.modify_based_on_request(request)
        if weighting_responses:
            response["ok"] = True
            response["responses"].extend(weighting_responses)

        return response
