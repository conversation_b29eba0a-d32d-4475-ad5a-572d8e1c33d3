from escpos.printer import Usb
import os

# Coloque aqui o VENDOR_ID e PRODUCT_ID que você pega rodando 'lsusb'
VENDOR_ID = 0x1fc9
PRODUCT_ID = 0x2016

try:

# Substitua pelos valores da sua impressora USB
    printer = Usb(0x1fc9, 0x2016, 0)  # VendorID, ProductID, interface

    # Configurações comuns
    printer.set(align='center', font='a', width=2, height=2)

    def imprime_titulo(titulo):
        printer.text("\n====================\n")
        printer.text(titulo + "\n")
        printer.text("====================\n")

    # UPC-A (12 dígitos)
    imprime_titulo("UPC-A")
    printer.barcode('012345678905', 'UPC-A', width=2, height=80, pos='BELOW')

    # UPC-E (8 dígitos)
    imprime_titulo("UPC-E")
    printer.barcode('01234565', 'UPC-E', width=2, height=80, pos='BELOW')

    # EAN-13 (13 dígitos)
    imprime_titulo("EAN13")
    printer.barcode('7891234567890', 'EAN13', width=2, height=80, pos='BELOW')

    # EAN-8 (8 dígitos)
    imprime_titulo("EAN8")
    printer.barcode('78400030', 'EAN8', width=2, height=80, pos='BELOW')

    # CODE39 (alfanumérico)
    imprime_titulo("CODE39")
    printer.barcode('BR1234Z', 'CODE39', width=2, height=80, pos='BELOW')

    # ITF (apenas números, número par)
    imprime_titulo("ITF")
    printer.barcode('12345678', 'ITF', width=2, height=80, pos='BELOW')

    # CODABAR (NW7)
    imprime_titulo("CODABAR")
    printer.barcode('A123456A', 'CODABAR', width=2, height=80, pos='BELOW')

    # CODE93
    imprime_titulo("CODE93")
    printer.barcode('ABCD1234', 'CODE93', width=2, height=80, pos='BELOW')

    # Final
    printer.text("\n\nTestes finalizados.\n")
    printer.cut()
    print("✅ Impressão enviada com sucesso!")

except Exception as e:
    print("❌ Erro ao imprimir:", e)