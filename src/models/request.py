"""Modelo de requisição para o sistema de controle de estoque."""

from enum import Enum
from typing import Optional, Any
import re
from pydantic import BaseModel, model_validator


class Action(str, Enum):
    """Enum para ação da requisição."""

    POST = "POST"
    GET = "GET"
    DELETE = "DELETE"
    PUT = "PUT"
    COMMAND = "COMMAND"


class CommandPath(str, Enum):
    """Enum para os paths do tipo COMMAND"""

    FLOW = "go_flow"
    CONFIG = "go_config"
    FAMILY = "mode_family"
    FAMILY_ONE_ON = "mode_family_one_on"
    FAMILY_TWO_ON = "mode_family_two_on"
    FAMILY_ONE_OFF = "mode_family_one_off"
    FAMILY_TWO_OFF = "mode_family_two_off"
    FAMILY_ONE_STANDBY_ON = "mode_family_one_standby_on"
    FAMILY_TWO_STANDBY_ON = "mode_family_two_standby_on"
    FAMILY_ONE_STANDBY_OFF = "mode_family_one_standby_off"
    FAMILY_TWO_STANDBY_OFF = "mode_family_two_standby_off"
    FAMILY_MODE_CUT_ONE = "mode_family_cut_one"
    FAMILY_MODE_CUT_TWO = "mode_family_cut_two"


class Path(str, Enum):
    """Enum para caminho da requisição."""

    PRODUCTS = "product"
    EMAIL = "email"
    WEIGHT_BY_SOFTWARE = "weight_by_software"
    SEND_EMAIL = "send_email"
    USB_PRINTER = "usb_printer"
    USB_SCALER = "usb_scaler"
    USB_DEVICES = "usb_devices"
    SERIAL_DEVICES = "serial_devices"
    PRINTER_LAYOUTS = "config_printer_layouts"
    CONFIG = "config"
    TESTE = "teste"


class Request(BaseModel):
    """Modelo de requisição para o sistema de controle de estoque."""

    action: Action
    path: str  # Changed from Path Enum to string
    data: Optional[Any] = None  # Ajustado para aceitar qualquer tipo

    @classmethod
    @model_validator(mode="before")
    def validate_request(cls, values):
        """Valida a requisição."""
        action = values.get("action")
        path = values.get("path")
        data = values.get("data")

        # Validate path based on action
        if action in (Action.GET, Action.DELETE) and Path.PRODUCTS in path:
            if not re.match(r"^products(/\d+)?$", path):
                raise ValueError(
                    "Path inválido para a ação 'GET'. Deve ser 'products' ou 'products/<id>'."
                )
        else:
            if path not in [item.value for item in Path]:
                raise ValueError(f"Path inválido para a ação '{action}'.")
            if action == Action.GET and data is not None:
                raise ValueError("A ação 'GET' não requer o campo 'data'.")

        return values

    @classmethod
    @model_validator(mode="before")
    def validate_data_content(cls, values):
        """Valida o conteúdo do campo 'data'."""
        path = values.get("path")
        action = values.get("action")
        # data = values.get('data')

        if re.match(r"^products/\d+$", path) and action == Action.GET:
            # No additional data validation needed for GET with product ID
            return values

        return values
