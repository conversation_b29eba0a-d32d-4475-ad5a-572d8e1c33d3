"""Modelo de email."""

from typing import Annotated
from pydantic import BaseModel, BeforeValidator
from pydantic_core import PydanticCustomError


def validate_email(v):
    """Valida o formato de e-mail com mensagens em português."""
    if not isinstance(v, str):
        raise PydanticCustomError(
            "email_type",
            "`` valor do e-mail deve ser uma string`",
        )
    if "@" not in v:
        raise PydanticCustomError(
            "email_missing_at",
            "`E-mail inválido: deve conter o caractere @`",
        )
    local_part, domain = v.rsplit("@", 1)
    if not local_part:
        raise PydanticCustomError(
            "email_empty_local",
            "`E-mail inválido: parte local antes do @ não pode estar vazia`",
        )
    if not domain or "." not in domain:
        raise PydanticCustomError(
            "email_invalid_domain",
            "`E-mail inválido: domínio após o @ deve conter pelo menos um ponto`",
        )
    return v


# Tipo personalizado com validador
CustomEmailStr = Annotated[str, BeforeValidator(validate_email)]


class Email(BaseModel):
    """Modelo de email."""

    value: CustomEmailStr
    active: bool
    email_id: int
