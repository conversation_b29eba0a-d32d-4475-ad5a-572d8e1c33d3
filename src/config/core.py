"""Módulo de configuração do sistema."""

from typing import Dict
import logging
import json

from services.files.core import Files

logger = logging.getLogger(__name__)


class Config:
    """
    Objeto de validação dos arquivos de configuração do sistema.

    Attributes:
    ----------
    products: Dict[str, Dict]
        Dicionário de produtos.
    usb_printer: str
        Porta USB da impressora.
    usb_scaler: str
        Porta USB da balança.
    from_file: bool
        Indica se o sistema foi inicializado a partir de arquivos.
    warning: Optional[str]
        Mensagem de erro, caso haja.
    missing_files: Optional[List[str]]
        Lista de arquivos faltantes, caso haja.
    """

    def __init__(
        self,
        products: Dict[str, Dict] = None,
        usb_printer: str = "",
        usb_scaler: str = "",
    ):
        self.files = Files()
        if products is None:
            products = {}
        if self._init_from_file():
            self.from_file = True
            return
        self.from_file = False
        for val in (
            "products",
            "usb_printer",
            "usb_scaler",
        ):
            if not hasattr(self, val):
                setattr(self, val, None)

    def _init_from_file(self) -> bool:
        """
        Método para inicializar o sistema a partir de arquivos.

        Returns:
        -------
        bool
            True se os arquivos forem lidos com sucesso, False caso contrário.
        """
        try:
            products: dict = self.files.get("product", pure=True).get("data")
            printer = self.files.get("usb_printer").get("data")
            scaler = self.files.get("usb_scaler").get("data")
        except Exception as e:  # pylint: disable=broad-except
            logger.warning("Erro ao ler arquivos de configuração: %s", e)
            self.warning = "Erro ao ler arquivos de configuração."
            return False
        if products in (None, []) or printer in (None, "") or scaler in (None, ""):
            logger.warning("Alguns arquivos de configuração podem estar faltando.")
            missing_files = []
            if products in (None, []):
                missing_files.append("products")
            else:
                self.products = products
            if printer in (None, ""):
                missing_files.append("usb_printer")
            else:
                self.usb_printer = printer
            if scaler in (None, ""):
                missing_files.append("usb_scaler")
            else:
                self.usb_scaler = scaler
            self.warning = "Alguns arquivos de configuração podem estar faltando."
            self.missing_files = missing_files
            return False

        else:
            logger.info("Arquivos de configuração lidos do arquivo com sucesso.")
            product_ids = list(products.keys())
            for product_id in product_ids:
                if product_id is None:
                    logger.warning("IDs de produtos não encontrados.")
                    self.warning = "IDs de produtos não encontrados."
                    return False
            self.products = products
            self.usb_printer = printer
            self.usb_scaler = scaler
            logger.info("Sistema inicializado a partir de arquivos.")
            return True

    def validate_config(self) -> dict:
        """
        Método para configurar o sistema.

        Returns:
        -------
        dict
            Dicionário com o status da configuração.
        """
        self.files.post(
            "product", json.dumps(list(self.products.values()))
        )
        self.files.post("usb_printer", self.usb_printer)
        self.files.post("usb_scaler", self.usb_scaler)
        logger.info("Configuração salva com sucesso.")
        if hasattr(self, "warning"):
            status = {"status": "warning", "message": self.warning, "page": "config"}
            if hasattr(self, "missing_files"):
                status["missing_files"] = self.missing_files
            return status
        return {"status": "success", "message": "Configuração salva com sucesso."}
