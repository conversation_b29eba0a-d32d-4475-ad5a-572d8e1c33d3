import pytest
from src.weighting.core import Weighting
from src.models.weighting_status import WeightingStatus


@pytest.mark.integration
def test_weighting_state_stable():
    w = Weighting(threshold=0)
    # Simula pesos estáveis (iguais)
    w.add_weight("12345   0   06789")
    state = w.current_state()
    assert state["status"] == WeightingStatus.STABLE.value
    assert state["peso"] == "12.345"
    assert state["preco"] == "67.89"


@pytest.mark.integration
def test_weighting_state_stabilizing():
    w = Weighting(threshold=0)
    w.add_weight("IIIIIIIIIIIIIIIIIIII")
    _ = w.current_state()
    w.add_weight("IIIIIIIIIIIIIIIIIIII")
    _ = w.current_state()
    w.add_weight("IIIIIIIIIIIIIIIIIIII")
    state = w.current_state()
    assert state["status"] == WeightingStatus.STABILIZING.value


@pytest.mark.integration
def test_weighting_state_waiting():
    w = Weighting(threshold=0)
    w.add_weight("00000   0   00000")
    _ = w.current_state()
    w.add_weight("00000   0   00000")
    _ = w.current_state()
    w.add_weight("00000   0   00000")
    state = w.current_state()
    assert state["status"] == WeightingStatus.WAITING.value


@pytest.mark.integration
def test_weighting_state_thanks():
    w = Weighting(threshold=0)
    w.add_weight("12345   0   06789")
    # Primeira chamada: stable
    state1 = w.current_state()
    assert state1["status"] == WeightingStatus.STABLE.value
    # Segunda chamada: thanks
    w.add_weight("12345   0   06789")
    state2 = w.current_state()
    assert state2["status"] == WeightingStatus.THANKS.value


@pytest.mark.integration
def test_weighting_state_stable_with_small_variation():
    w = Weighting(threshold=0)
    # Dois pesos estáveis
    w.add_weight("12345   0   06789")
    _ = w.current_state()
    w.add_weight("12345   0   06789")

    # Nesse ponto deve estar agradecendo
    state = w.current_state()
    assert state["status"] == WeightingStatus.THANKS.value
    # Um peso ligeiramente diferente
    w.add_weight("IIIIIIIIIIIIIIIIIIII")
    _ = w.current_state()
    w.add_weight("12346   0   06789")

    state = w.current_state()
    assert state["status"] == WeightingStatus.THANKS.value


@pytest.mark.integration
def test_weighting_state_stable_with_interference():
    w = Weighting(threshold=0)
    # Dois pesos estáveis
    w.add_weight("12345   0   06789")
    _ = w.current_state()
    w.add_weight("12345   0   06789")

    # Nesse ponto deve estar agradecendo
    state = w.current_state()
    assert state["status"] == WeightingStatus.THANKS.value
    # Um peso ligeiramente diferente
    w.add_weight("IIIIIIIIIIIIIIIIIIII")
    _ = w.current_state()
    w.add_weight("12345   0   06789")

    state = w.current_state()
    assert state["status"] == WeightingStatus.THANKS.value
