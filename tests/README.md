# Tests for PesaePronto

This directory contains automated tests for the PesaePronto application.

## Running Tests

To run the tests, make sure you have installed the required dependencies:

```bash
pip install -r tests/requirements.txt
```

Then run the tests using pytest:

```bash
# Run from the python directory
pytest tests/

# For test coverage report
pytest tests/ --cov=src --cov-report=term-missing
```

## Test Structure

- `conftest.py`: Contains shared fixtures for tests
- `test_files.py`: Tests for the Files class
- `test_config.py`: Tests for the Config class
- `test_tasks.py`: Tests for WebSocket tasks
- `test_printer.py`: Tests for printer functionality

## Critical Components

The tests focus on these critical aspects of the application:

1. Configuration management - Ensuring the system can be properly configured
2. File operations - Testing the core file handling functions
3. Serial communication - Testing the serial data reading and processing
4. Printing functionality - Testing the thermal printer output generation
