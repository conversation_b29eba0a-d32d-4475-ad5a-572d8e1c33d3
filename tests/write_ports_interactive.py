import serial
import time
import json
import os
import signal
import sys
from pathlib import Path


class SimuladorBalança:
    def __init__(self, porta, config_file="/tmp/simulation_config.json"):
        self.porta = porta
        self.config_file = config_file
        self.ser = None
        self.running = True
        self.paused = False
        
        # Configurações padrão
        self.oscillation_count = 10
        self.stability_count = 7
        self.zero_count = 3
        self.delay = 0.3
        self.baudrate = 9600

        # Peso e pronto
        self.weight_only_mode = False
        
        # Carrega configurações do arquivo
        self.load_config()
        
        # Configura handlers de sinal
        signal.signal(signal.SIGTERM, self.signal_handler)
        signal.signal(signal.SIGINT, self.signal_handler)
    
    def load_config(self):
        """Carrega configurações do arquivo JSON"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                    self.oscillation_count = config.get('oscillation_count', self.oscillation_count)
                    self.stability_count = config.get('stability_count', self.stability_count)
                    self.zero_count = config.get('zero_count', self.zero_count)
                    self.delay = config.get('delay', self.delay)
                    self.baudrate = config.get('baudrate', self.baudrate)
                    if 'port' in config:
                        self.porta = config['port']
                    self.weight_only_mode = config.get('weight_only_mode', self.weight_only_mode)
                print(f"Configurações carregadas: {config}")
        except Exception as e:
            print(f"Aviso: Não foi possível carregar configurações: {e}")
    
    def signal_handler(self, signum, frame):
        """Handler para sinais de interrupção"""
        print(f"\nRecebido sinal {signum}. Encerrando simulação...")
        self.running = False
        if self.ser and self.ser.is_open:
            self.ser.close()
            print(f"Porta {self.porta} fechada.")
        sys.exit(0)
    
    def check_stop_file(self):
        """Verifica se existe arquivo de parada"""
        return os.path.exists("/tmp/simulation_stop")
    
    def connect(self):
        """Conecta à porta serial"""
        try:
            self.ser = serial.Serial(
                port=self.porta, 
                baudrate=self.baudrate, 
                timeout=1
            )
            print(f"Conectado à porta {self.porta} com baudrate {self.baudrate}")
            return True
        except serial.SerialException as e:
            print(f"Erro ao conectar na porta {self.porta}: {e}")
            return False
    
    def send_data(self, data, description=""):
        """Envia dados para a porta serial"""
        if not self.ser or not self.ser.is_open:
            return False
        
        try:
            self.ser.write(data.encode("utf-8") + b"\n")
            print(f"Dados enviados ({description}): {data}")
            return True
        except Exception as e:
            print(f"Erro ao enviar dados: {e}")
            return False
    
    def wait_with_pause_check(self, seconds):
        """Aguarda o tempo especificado, verificando se foi pausado"""
        start_time = time.time()
        while time.time() - start_time < seconds and self.running:
            time.sleep(0.1)
            # Verifica se há um arquivo de pausa
            if os.path.exists("/tmp/simulation_paused"):
                self.paused = True
                print("Simulação pausada...")
                while os.path.exists("/tmp/simulation_paused") and self.running:
                    time.sleep(0.1)
                self.paused = False
                print("Simulação retomada...")
            
            # Verifica se há arquivo de parada
            if self.check_stop_file():
                print("Arquivo de parada detectado. Encerrando...")
                self.running = False
                break
    
    def fase_oscilacao(self):
        """Executa a fase de oscilação"""
        print(f"\n=== FASE DE OSCILAÇÃO ({self.oscillation_count} iterações) ===")
        for i in range(self.oscillation_count):
            if not self.running:
                break
            
            dados = "IIIIIIIIIIIIIIIIIIII" if not self.weight_only_mode else "IIIII"
            if self.send_data(dados, f"oscilação {i+1}/{self.oscillation_count}"):
                self.wait_with_pause_check(self.delay)
    
    def fase_estabilidade(self):
        """Executa a fase de estabilidade"""
        print(f"\n=== FASE DE ESTABILIDADE ({self.stability_count} iterações) ===")
        dados = "12345   000   67890" if not self.weight_only_mode else "12345"
        for i in range(self.stability_count):
            if not self.running:
                break
            
            if self.send_data(dados, f"estável {i+1}/{self.stability_count}"):
                self.wait_with_pause_check(self.delay)
    
    def fase_zero(self):
        """Executa a fase de peso zero"""
        print(f"\n=== FASE DE PESO ZERO ({self.zero_count} iterações) ===")
        dados = "00000   000   00000" if not self.weight_only_mode else "00000"
        for i in range(self.zero_count):
            if not self.running:
                break
            
            if self.send_data(dados, f"zero {i+1}/{self.zero_count}"):
                self.wait_with_pause_check(self.delay)
    
    def simular_ciclo(self):
        """Executa um ciclo completo de simulação"""
        if not self.connect():
            return False
        
        try:
            while self.running:
                # Verifica se há arquivo de parada
                if self.check_stop_file():
                    print("Arquivo de parada detectado. Encerrando simulação...")
                    break
                
                print(f"\n{'='*50}")
                print("INICIANDO NOVO CICLO DE SIMULAÇÃO")
                print(f"{'='*50}")
                
                # Executa as três fases
                self.fase_oscilacao()
                if not self.running:
                    break
                
                self.fase_estabilidade()
                if not self.running:
                    break
                
                self.fase_zero()
                if not self.running:
                    break
                
                print(f"\n{'='*50}")
                print("CICLO COMPLETO - INICIANDO PRÓXIMO CICLO")
                print(f"{'='*50}")
                
                # Pausa entre ciclos
                self.wait_with_pause_check(2.0)
        
        except KeyboardInterrupt:
            print("\nInterrompido pelo usuário.")
        except Exception as e:
            print(f"Erro inesperado: {e}")
        finally:
            if self.ser and self.ser.is_open:
                self.ser.close()
                print(f"Porta {self.porta} fechada.")
        
        return True


def main():
    """Função principal"""
    if len(sys.argv) < 2:
        print("Uso: python write_ports_interactive.py <porta_serial>")
        sys.exit(1)
    
    porta_serial = sys.argv[1]
    
    print(f"Iniciando simulador interativo na porta: {porta_serial}")
    print("Configurações serão carregadas de /tmp/simulation_config.json")
    print("Use Ctrl+C para parar a simulação")
    
    simulador = SimuladorBalança(porta_serial)
    simulador.simular_ciclo()


if __name__ == "__main__":
    main()
