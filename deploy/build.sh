#!/bin/bash
pip install -r requirements.txt
pip install pyinstaller

cd src

# Gerar o arquivo .spec com a versão inserida dinamicamente
cat <<EOF > ./balanca_smart.spec
# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_submodules
import os
import re

VERSION = os.getenv('VERSION', 'v0.0.0')

pattern = r'^v\d+\.\d+\.\d+$'
if not re.match(pattern, VERSION):
    raise ValueError('VERSION does not match the required pattern vx.x.x')

PLATFORM = os.getenv('PLATFORM', 'linux')

if PLATFORM == 'raspberry':
    libusb_path = '/usr/lib/aarch64-linux-gnu/libusb-1.0.so'
elif PLATFORM == 'macos':
    libusb_path = '/opt/homebrew/opt/libusb/lib/libusb-1.0.dylib'
else:
    libusb_path = '/usr/lib/libusb-1.0.so'

a = Analysis(
    ['entrypoint.py'],
    pathex=['.'],
    binaries=[(libusb_path, '.')],  # Inclui libusb-1.0.so
    datas=[
        ('services/printers/capabilities.json', 'escpos/'),
        ('fonts', 'fonts'),  # Inclui a pasta de fontes
    ],
    hiddenimports=['libusb', 'usb.backend.libusb1'],  # Inclui o backend específico
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name=f'balanca_smart',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name=f'balanca_smart',
)
EOF

# Buildar o executável
pyinstaller ./balanca_smart.spec
