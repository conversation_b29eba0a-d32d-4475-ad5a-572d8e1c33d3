image: python:3.12

variables:
  # Valor default
  PLATFORM: "raspberry"

stages:
  # - test
  - build
  - deploy
  - validate_branch

# Test job to run pytest and generate coverage reports
# test:
#   stage: test
#   script:
#     - pip install -r requirements.txt
#     - pip install -r tests/requirements.txt
#     - pip install pytest-cov pytest-xdist
#     - pytest tests/ --cov=src --cov-report=term --cov-report=xml:test_results/coverage.xml --junitxml=test_results/report.xml -v
#     - TEST_FAILS=$(grep 'failures=' test_results/report.xml | head -n 1 | awk -F'failures="' '{print $2}' | awk -F'"' '{print $1}')
#     - |
#       echo "Testes falhos: $TEST_FAILS"
#     - |
#       [ "${TEST_FAILS:-0}" -gt 0 ] && exit 1 || echo "Todos os testes passaram."
#   coverage: '/(?i)total.*? (100(?:\.0+)?|[1-9]?\d(?:\.\d+)?)%/'
#   artifacts:
#     reports:
#       coverage_report:
#         coverage_format: cobertura
#         path: test_results/coverage.xml
#       junit: test_results/report.xml
#     paths:
#       - test_results/
#     expire_in: 1 week
#   before_script:
#     - apt-get update -qq
#     - apt-get install -qq -y build-essential git
#     - mkdir -p test_results

# Build para LINUX
build_linux:
  stage: build
  tags:
    - pc
  rules:
    - if: '$PLATFORM == "linux" && $VERSION'
      when: on_success
  before_script:
    - apt-get update -qq
    - apt-get install -qq -y openssh-client build-essential git binutils
  script:
    - pwd
    - ls -l
    - ls -l src
    - rm -rf dist build balanca_smart.spec
    - chmod +x ./deploy/build.sh
    - ./deploy/build.sh
  artifacts:
    paths:
      - dist/
      - build/
      - balanca_smart.spec
    expire_in: 1 hour

# Build para RASPBERRY
build_raspberry:
  image: python:3.12-slim
  stage: build
  # tags:
  #   - pc
  services:
    - docker:20.10.16-dind
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ""
  rules:
    - if: '$PLATFORM == "raspberry" && $VERSION'
      when: on_success
  before_script:
    - apt-get update -qq
    - apt-get install -qq -y docker.io openssh-client build-essential git binutils qemu-user-static
  script:
    # Registra os binários do QEMU
    - docker run --rm --privileged multiarch/qemu-user-static --reset -p yes
    # Executa o build em um contêiner ARM64 usando a imagem arm64v8/python:3.12-slim
    - |
      docker run --platform linux/arm64 --rm -v $(pwd):/app -e VERSION=$VERSION -e PLATFORM=$PLATFORM -w /app arm64v8/python:3.12-slim bash -c "
        apt-get update && \
        apt-get install -y binutils libusb-1.0-0 libusb-1.0-0-dev && \
        chmod +x ./deploy/build.sh && \
        ./deploy/build.sh
      "
  artifacts:
    paths:
      - src/dist/
      - src/build/
      - src/balanca_smart.spec
    expire_in: 1 hour

build_windows:
  stage: build
  tags:
    - saas-windows-medium-amd64
  rules:
    - if: '$PLATFORM == "windows" && $VERSION'
      when: on_success
  before_script:
    - python -m pip install --upgrade pip
    - pip install -r requirements.txt pyinstaller
  script:
    - cd src
    - pyinstaller ./balanca_smart.spec
  artifacts:
    paths:
      - src/dist/
      - src/build/
      - src/balanca_smart.spec
    expire_in: 1 hour


# Job de Deploy (depende do job de build que foi executado)
deploy:
  stage: deploy
  # tags:
  #   - pc
  needs:
    # - job: test
    #   artifacts: true
    - job: build_linux
      optional: true
    - job: build_raspberry
      optional: true
    - job: build_windows
      optional: true
  before_script:
    - apt-get update -qq
    - apt-get install -qq -y openssh-client build-essential git binutils
    - eval $(ssh-agent -s)
    - echo "$PYTHON_SSH_DEPLOY_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - ssh-keyscan gitlab.com >> ~/.ssh/known_hosts
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "CI Runner"
  script:
    - <NAME_EMAIL>:v_m1/build.git .build
    - rm -rf .build/$VERSION/back/*
    - mkdir -p .build/$VERSION/back
    - mkdir -p .build/latest/back
    - mkdir -p .build/stable/back
    # Copia a última latest para stable
    # TODO: Adicionar variável de controle para não fazer isso se eu quiser
    - cp -r .build/latest/back/* .build/stable/back
    # Manda o build para a versão definida
    - cp -r src/dist/balanca_smart/* .build/$VERSION/back
    # Manda o build para a latest
    - cp -r src/dist/balanca_smart/* .build/latest/back
    - cd .build
    - git fetch origin
    - git add .
    - git commit -m "Release version $VERSION"
    - git push origin main
  rules:
    - if: '$CI_COMMIT_REF_NAME == "main" && $VERSION'
      when: on_success
  dependencies:
    # - test
    - build_linux
    - build_raspberry
  artifacts:
    paths:
      - src/build/
    expire_in: 1 hour

# Job para validação de branch
validate_branch:
  stage: validate_branch
  script:
    - echo "Branch - $CI_COMMIT_REF_NAME - OK"
  rules:
    - if: '$CI_COMMIT_REF_NAME != "main"'
      when: on_success
