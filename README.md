# Pesa e Pronto


## Como rodar a aplicação

1. Navegue até a pasta `python`:
    ```
    cd ~/Documents/V&M/python
    ```

2. Execute o script de configuração do ambiente de testes:
    ```
    ./scripts/setup_testing_environment.sh
    ```
    Esse comando exibirá uma linha semelhante a:
    ```
    export PRINTER_SERIAL_PORT=/dev/ttys006
    ```

3. Abra um novo terminal, cole e execute o comando `export` exibido acima para definir a variável de ambiente.

4. No mesmo terminal onde você executou o `export`, rode a aplicação:
    ```
    uv run src/entrypoint.py
    PYTHONPATH=./src uv run -m entrypoint
    ```

5. Variáveis de ambiente:

- PESA_E_PRONTO_API:
    - Padrão: "https://api.pesaepronto.com.br"
    - Descrição: Esta variável define para onde serão feitas as requisições para a API do Pesa e Pronto.


Sistema para automação de controle de pesagem, integração com balanças seriais, geração de relatórios, impressão de comandas e comunicação em tempo real via WebSocket.

## Fluxo Principal
- **Servidor WebSocket**: O ponto de entrada (`src/entrypoint.py`) inicia um servidor WebSocket que recebe comandos e envia dados em tempo real para clientes conectados (ex: frontend).
- **Leitura Serial**: O sistema lê dados da balança via porta serial, processa o peso e calcula preços automaticamente.
- **Controle de Estado**: Gerencia estabilidade da pesagem, status e eventos de pesagem.
- **Impressão e Relatórios**: Gera e imprime comandas, além de relatórios em PDF e gráficos.
- **Envio de Dados**: Permite envio de dados e relatórios para uma API externa via comandos CLI.

## Principais Comandos

```sh
# Iniciar o servidor WebSocket (aplicação principal)
python src/entrypoint.py

# Enviar dados para a API externa
python src/entrypoint.py --send_data
```

## Instalação
1. Instale o Python 3.12+
2. Instale as dependências:
```sh
pip install -r requirements.txt
```
Para recursos de PDF e relatórios:
```sh
pip install fpdf pandas seaborn matplotlib
```

> [!NOTE]  
> Para uso com `uv`, apenas faça: `uv sync`

## Ambiente de testes
Scripts em `scripts/` simulam portas seriais para testes locais:
```sh
./scripts/setup_testing_environment.sh
```

## Testes automatizados
```sh
pip install -r tests/requirements.txt
pytest tests/
```

## Observações
- O sistema depende de configuração correta das portas seriais e dispositivos.
- Para dúvidas, consulte o responsável pelo projeto.
